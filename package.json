{"name": "smalt-alert-system", "version": "1.0.0", "description": "Comprehensive web-based alert system with AI integration", "main": "server/src/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && npm start", "test": "npm run test:server && npm run test:client", "test:server": "cd server && npm test", "test:client": "cd client && npm test", "setup": "npm install && cd server && npm install && cd ../client && npm install", "db:migrate": "cd server && npm run db:migrate", "db:seed": "cd server && npm run db:seed", "db:reset": "cd server && npm run db:reset", "lint": "npm run lint:server && npm run lint:client", "lint:server": "cd server && npm run lint", "lint:client": "cd client && npm run lint", "clean": "rm -rf node_modules server/node_modules client/node_modules client/dist"}, "keywords": ["alerts", "notifications", "vue", "nodejs", "postgresql", "ai", "openai"], "author": "SMALT Alert System", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["server", "client"]}