# SMALT Alert System

A comprehensive web-based alert system with AI integration, supporting multiple data sources and notification channels.

## Features

- **User Management**: Registration, login, MFA, profile management
- **Alert System**: Custom alerts with configurable conditions
- **Data Sources**: Yahoo Finance, OpenWeatherMap, NewsAPI
- **AI Integration**: OpenAI-powered suggestions and optimizations
- **Notifications**: Email (Resend) and browser push notifications
- **Security**: MFA, secure token handling, encrypted API keys

## Tech Stack

- **Frontend**: Vue.js 3 + Naive UI
- **Backend**: Node.js + Express
- **Database**: PostgreSQL (NeonDB)
- **Authentication**: JWT + TOTP/SMS MFA
- **Documentation**: Swagger/OpenAPI

## Quick Start

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd smalt-alert-system
   npm run setup
   ```

2. **Environment Configuration**
   ```bash
   cp server/.env.example server/.env
   cp client/.env.example client/.env
   # Edit the .env files with your configuration
   ```

3. **Database Setup**
   ```bash
   cd server
   npm run db:migrate
   npm run db:seed
   ```

4. **Start Development**
   ```bash
   npm run dev
   ```

## Environment Variables

### Server (.env)
```
NODE_ENV=development
PORT=3000
DATABASE_URL=postgresql://username:password@localhost:5432/smalt_alerts
JWT_SECRET=your-jwt-secret
JWT_REFRESH_SECRET=your-refresh-secret

# External APIs
YAHOO_FINANCE_API_KEY=your-yahoo-finance-key
OPENWEATHER_API_KEY=your-openweather-key
NEWS_API_KEY=your-news-api-key
OPENAI_API_KEY=your-openai-key

# Notifications
RESEND_API_KEY=your-resend-key
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key

# Security
ENCRYPTION_KEY=your-32-char-encryption-key
MFA_ISSUER=SMALT Alert System
```

### Client (.env)
```
VITE_API_BASE_URL=http://localhost:3000/api
VITE_VAPID_PUBLIC_KEY=your-vapid-public-key
```

## Project Structure

```
smalt-alert-system/
├── client/                 # Vue.js frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── views/          # Page components
│   │   ├── stores/         # Pinia stores
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
├── server/                 # Node.js backend
│   ├── src/
│   │   ├── controllers/    # Route controllers
│   │   ├── middleware/     # Express middleware
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   ├── routes/         # API routes
│   │   └── utils/          # Utility functions
│   ├── migrations/         # Database migrations
│   └── seeds/              # Database seeds
└── docs/                   # Documentation
```

## API Documentation

Once the server is running, visit `http://localhost:3000/api-docs` for interactive API documentation.

## Development

- **Frontend**: `npm run client:dev` - Runs on http://localhost:5173
- **Backend**: `npm run server:dev` - Runs on http://localhost:3000
- **Both**: `npm run dev` - Runs both concurrently

## Testing

```bash
npm test                    # Run all tests
npm run test:server         # Server tests only
npm run test:client         # Client tests only
```

## Deployment

1. Build the frontend:
   ```bash
   npm run build
   ```

2. Set production environment variables

3. Start the production server:
   ```bash
   npm start
   ```

## License

MIT License - see LICENSE file for details.
