-- Initial data for SMALT Alert System

-- Insert default alert categories
INSERT INTO alert_categories (name, description, icon, color) VALUES
('Stock Alerts', 'Stock price and financial market alerts', 'trending-up', '#10B981'),
('Weather Alerts', 'Weather condition and forecast alerts', 'cloud-rain', '#3B82F6'),
('News Alerts', 'News and media monitoring alerts', 'newspaper', '#F59E0B'),
('Custom Alerts', 'User-defined custom alerts', 'settings', '#8B5CF6'),
('AI Insights', 'AI-powered analysis and recommendations', 'brain', '#EF4444');

-- Insert default data sources
INSERT INTO data_sources (name, type, api_endpoint, rate_limit_per_hour, configuration) VALUES
('Yahoo Finance', 'stock', 'https://query1.finance.yahoo.com/v8/finance/chart/', 2000, 
 '{"supported_symbols": ["stocks", "crypto", "forex"], "rate_limit": 2000, "cache_duration": 300}'),
 
('OpenWeatherMap', 'weather', 'https://api.openweathermap.org/data/2.5/', 1000,
 '{"supported_types": ["current", "forecast", "alerts"], "rate_limit": 1000, "cache_duration": 600}'),
 
('NewsAPI', 'news', 'https://newsapi.org/v2/', 1000,
 '{"supported_endpoints": ["everything", "top-headlines", "sources"], "rate_limit": 1000, "cache_duration": 900}'),
 
('Custom API', 'custom', null, 500,
 '{"description": "User-defined custom data sources", "rate_limit": 500, "cache_duration": 300}');

-- Insert default system settings
INSERT INTO system_settings (key, value, description) VALUES
('max_alerts_per_user', '50', 'Maximum number of alerts a user can create'),
('max_notifications_per_hour', '20', 'Maximum notifications per user per hour'),
('default_alert_frequency', '5', 'Default alert check frequency in minutes'),
('email_rate_limit', '100', 'Maximum emails per user per day'),
('push_rate_limit', '200', 'Maximum push notifications per user per day'),
('mfa_required', 'false', 'Whether MFA is required for all users'),
('password_min_length', '8', 'Minimum password length'),
('session_timeout', '15', 'Session timeout in minutes'),
('refresh_token_lifetime', '7', 'Refresh token lifetime in days'),
('api_rate_limit_window', '15', 'API rate limit window in minutes'),
('api_rate_limit_max', '100', 'Maximum API requests per window'),
('maintenance_mode', 'false', 'System maintenance mode flag'),
('registration_enabled', 'true', 'Whether new user registration is enabled'),
('ai_features_enabled', 'true', 'Whether AI features are enabled'),
('notification_retry_attempts', '3', 'Maximum notification retry attempts');

-- Create admin user (password: admin123 - change in production!)
-- Password hash for 'admin123' using bcrypt
INSERT INTO users (
    email, 
    password_hash, 
    first_name, 
    last_name, 
    is_email_verified,
    created_at
) VALUES (
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- admin123
    'System',
    'Administrator',
    true,
    CURRENT_TIMESTAMP
);

-- Sample alert conditions for demonstration
-- These will be used as templates for users
INSERT INTO system_settings (key, value, description) VALUES
('sample_stock_conditions', 
 '[
   {"field": "price", "operator": "gt", "value": "100", "description": "Price above $100"},
   {"field": "price", "operator": "lt", "value": "50", "description": "Price below $50"},
   {"field": "volume", "operator": "gt", "value": "1000000", "description": "Volume above 1M"},
   {"field": "change_percent", "operator": "gt", "value": "5", "description": "Daily change above 5%"},
   {"field": "change_percent", "operator": "lt", "value": "-5", "description": "Daily change below -5%"}
 ]',
 'Sample stock alert conditions'),

('sample_weather_conditions',
 '[
   {"field": "temperature", "operator": "gt", "value": "30", "description": "Temperature above 30°C"},
   {"field": "temperature", "operator": "lt", "value": "0", "description": "Temperature below 0°C"},
   {"field": "humidity", "operator": "gt", "value": "80", "description": "Humidity above 80%"},
   {"field": "wind_speed", "operator": "gt", "value": "50", "description": "Wind speed above 50 km/h"},
   {"field": "condition", "operator": "eq", "value": "rain", "description": "Rainy weather"},
   {"field": "condition", "operator": "eq", "value": "snow", "description": "Snowy weather"}
 ]',
 'Sample weather alert conditions'),

('sample_news_conditions',
 '[
   {"field": "title", "operator": "contains", "value": "breaking", "description": "Breaking news"},
   {"field": "content", "operator": "contains", "value": "stock market", "description": "Stock market news"},
   {"field": "source", "operator": "eq", "value": "reuters", "description": "Reuters articles"},
   {"field": "category", "operator": "eq", "value": "technology", "description": "Technology news"},
   {"field": "sentiment", "operator": "lt", "value": "-0.5", "description": "Negative sentiment"}
 ]',
 'Sample news alert conditions');

-- Insert notification templates
INSERT INTO system_settings (key, value, description) VALUES
('email_templates',
 '{
   "alert_triggered": {
     "subject": "Alert Triggered: {{alert_name}}",
     "body": "Your alert \"{{alert_name}}\" has been triggered.\n\nCondition: {{condition}}\nCurrent Value: {{current_value}}\nTriggered At: {{triggered_at}}\n\nView your alerts: {{dashboard_url}}"
   },
   "welcome": {
     "subject": "Welcome to SMALT Alert System",
     "body": "Welcome {{first_name}}!\n\nYour account has been created successfully. You can now start creating custom alerts.\n\nGet started: {{dashboard_url}}"
   },
   "password_reset": {
     "subject": "Password Reset Request",
     "body": "You requested a password reset.\n\nClick here to reset your password: {{reset_url}}\n\nThis link expires in 1 hour."
   },
   "email_verification": {
     "subject": "Verify Your Email Address",
     "body": "Please verify your email address by clicking the link below:\n\n{{verification_url}}\n\nThis link expires in 24 hours."
   }
 }',
 'Email notification templates');

-- Insert default notification preferences
INSERT INTO system_settings (key, value, description) VALUES
('default_notification_preferences',
 '{
   "email": true,
   "push": true,
   "sms": false,
   "frequency": "immediate",
   "quiet_hours": {
     "enabled": false,
     "start": "22:00",
     "end": "08:00"
   },
   "digest": {
     "enabled": false,
     "frequency": "daily",
     "time": "09:00"
   }
 }',
 'Default notification preferences for new users');
