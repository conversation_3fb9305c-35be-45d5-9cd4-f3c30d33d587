const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const crypto = require('crypto');
const { query, transaction } = require('../config/database');
const { verifyToken } = require('../middleware/auth');
const { validateSchema, schemas, sanitizeInput } = require('../middleware/validation');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         email:
 *           type: string
 *           format: email
 *         firstName:
 *           type: string
 *         lastName:
 *           type: string
 *         isEmailVerified:
 *           type: boolean
 *         mfaEnabled:
 *           type: boolean
 *         createdAt:
 *           type: string
 *           format: date-time
 *     AuthResponse:
 *       type: object
 *       properties:
 *         user:
 *           $ref: '#/components/schemas/User'
 *         accessToken:
 *           type: string
 *         refreshToken:
 *           type: string
 *         expiresIn:
 *           type: number
 */

// Generate JWT tokens
const generateTokens = (userId) => {
  const accessToken = jwt.sign(
    { userId, type: 'access' },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '15m' }
  );

  const refreshToken = jwt.sign(
    { userId, type: 'refresh' },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' }
  );

  return { accessToken, refreshToken };
};

/**
 * @swagger
 * /auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - firstName
 *               - lastName
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 8
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *     responses:
 *       201:
 *         description: User registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       400:
 *         description: Validation error
 *       409:
 *         description: Email already exists
 */
router.post('/register', 
  sanitizeInput,
  validateSchema(schemas.userRegistration),
  async (req, res) => {
    try {
      const { email, password, firstName, lastName } = req.body;

      // Check if user already exists
      const existingUser = await query('SELECT id FROM users WHERE email = $1', [email]);
      if (existingUser.rows.length > 0) {
        return res.status(409).json({
          error: 'Email already exists',
          message: 'A user with this email address already exists'
        });
      }

      // Hash password
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      // Generate email verification token
      const emailVerificationToken = crypto.randomBytes(32).toString('hex');
      const emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Create user
      const result = await query(`
        INSERT INTO users (email, password_hash, first_name, last_name, email_verification_token, email_verification_expires)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, email, first_name, last_name, is_email_verified, mfa_enabled, created_at
      `, [email, passwordHash, firstName, lastName, emailVerificationToken, emailVerificationExpires]);

      const user = result.rows[0];

      // Generate tokens
      const { accessToken, refreshToken } = generateTokens(user.id);

      // Store refresh token
      await query(`
        INSERT INTO user_sessions (user_id, refresh_token, device_info, ip_address, expires_at)
        VALUES ($1, $2, $3, $4, $5)
      `, [
        user.id,
        refreshToken,
        JSON.stringify(req.headers['user-agent'] || {}),
        req.ip,
        new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
      ]);

      // TODO: Send verification email
      console.log(`Email verification token for ${email}: ${emailVerificationToken}`);

      res.status(201).json({
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          isEmailVerified: user.is_email_verified,
          mfaEnabled: user.mfa_enabled,
          createdAt: user.created_at
        },
        accessToken,
        refreshToken,
        expiresIn: 15 * 60 // 15 minutes in seconds
      });

    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({
        error: 'Registration failed',
        message: 'Internal server error during registration'
      });
    }
  }
);

/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: Login user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *               mfaToken:
 *                 type: string
 *                 description: Required if MFA is enabled
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       401:
 *         description: Invalid credentials
 *       423:
 *         description: Account locked
 */
router.post('/login',
  sanitizeInput,
  validateSchema(schemas.userLogin),
  async (req, res) => {
    try {
      const { email, password, mfaToken } = req.body;

      // Get user with login attempt info
      const userResult = await query(`
        SELECT id, email, password_hash, first_name, last_name, is_email_verified, 
               mfa_enabled, mfa_secret, login_attempts, locked_until
        FROM users 
        WHERE email = $1
      `, [email]);

      if (userResult.rows.length === 0) {
        return res.status(401).json({
          error: 'Invalid credentials',
          message: 'Email or password is incorrect'
        });
      }

      const user = userResult.rows[0];

      // Check if account is locked
      if (user.locked_until && new Date() < user.locked_until) {
        return res.status(423).json({
          error: 'Account locked',
          message: 'Account is temporarily locked due to too many failed login attempts'
        });
      }

      // Verify password
      const passwordValid = await bcrypt.compare(password, user.password_hash);
      if (!passwordValid) {
        // Increment login attempts
        const newAttempts = (user.login_attempts || 0) + 1;
        const lockUntil = newAttempts >= 5 ? new Date(Date.now() + 30 * 60 * 1000) : null; // Lock for 30 minutes after 5 attempts

        await query(`
          UPDATE users 
          SET login_attempts = $1, locked_until = $2 
          WHERE id = $3
        `, [newAttempts, lockUntil, user.id]);

        return res.status(401).json({
          error: 'Invalid credentials',
          message: 'Email or password is incorrect'
        });
      }

      // Verify MFA if enabled
      if (user.mfa_enabled) {
        if (!mfaToken) {
          return res.status(401).json({
            error: 'MFA required',
            message: 'Multi-factor authentication token is required'
          });
        }

        const verified = speakeasy.totp.verify({
          secret: user.mfa_secret,
          encoding: 'base32',
          token: mfaToken,
          window: parseInt(process.env.MFA_WINDOW) || 2
        });

        if (!verified) {
          return res.status(401).json({
            error: 'Invalid MFA token',
            message: 'Multi-factor authentication failed'
          });
        }
      }

      // Reset login attempts on successful login
      await query(`
        UPDATE users 
        SET login_attempts = 0, locked_until = NULL, last_login = CURRENT_TIMESTAMP 
        WHERE id = $1
      `, [user.id]);

      // Generate tokens
      const { accessToken, refreshToken } = generateTokens(user.id);

      // Store refresh token
      await query(`
        INSERT INTO user_sessions (user_id, refresh_token, device_info, ip_address, expires_at)
        VALUES ($1, $2, $3, $4, $5)
      `, [
        user.id,
        refreshToken,
        JSON.stringify(req.headers['user-agent'] || {}),
        req.ip,
        new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
      ]);

      res.json({
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          isEmailVerified: user.is_email_verified,
          mfaEnabled: user.mfa_enabled
        },
        accessToken,
        refreshToken,
        expiresIn: 15 * 60 // 15 minutes in seconds
      });

    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        error: 'Login failed',
        message: 'Internal server error during login'
      });
    }
  }
);

/**
 * @swagger
 * /auth/refresh:
 *   post:
 *     summary: Refresh access token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *       401:
 *         description: Invalid refresh token
 */
router.post('/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({
        error: 'Refresh token required',
        message: 'No refresh token provided'
      });
    }

    // Verify refresh token
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);

    // Check if refresh token exists in database
    const sessionResult = await query(`
      SELECT user_id FROM user_sessions 
      WHERE refresh_token = $1 AND expires_at > CURRENT_TIMESTAMP
    `, [refreshToken]);

    if (sessionResult.rows.length === 0) {
      return res.status(401).json({
        error: 'Invalid refresh token',
        message: 'Refresh token not found or expired'
      });
    }

    const userId = sessionResult.rows[0].user_id;

    // Generate new access token
    const { accessToken } = generateTokens(userId);

    res.json({
      accessToken,
      expiresIn: 15 * 60 // 15 minutes in seconds
    });

  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(401).json({
      error: 'Token refresh failed',
      message: 'Invalid or expired refresh token'
    });
  }
});

/**
 * @swagger
 * /auth/logout:
 *   post:
 *     summary: Logout user
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               refreshToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Logout successful
 *       401:
 *         description: Unauthorized
 */
router.post('/logout', verifyToken, async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (refreshToken) {
      // Remove specific session
      await query('DELETE FROM user_sessions WHERE refresh_token = $1', [refreshToken]);
    } else {
      // Remove all sessions for user
      await query('DELETE FROM user_sessions WHERE user_id = $1', [req.user.id]);
    }

    res.json({
      message: 'Logout successful'
    });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      error: 'Logout failed',
      message: 'Internal server error during logout'
    });
  }
});

/**
 * @swagger
 * /auth/verify-email:
 *   post:
 *     summary: Verify email address
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *     responses:
 *       200:
 *         description: Email verified successfully
 *       400:
 *         description: Invalid or expired token
 */
router.post('/verify-email', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        error: 'Token required',
        message: 'Email verification token is required'
      });
    }

    // Find user with this verification token
    const userResult = await query(`
      SELECT id, email, email_verification_expires
      FROM users
      WHERE email_verification_token = $1
    `, [token]);

    if (userResult.rows.length === 0) {
      return res.status(400).json({
        error: 'Invalid token',
        message: 'Email verification token is invalid'
      });
    }

    const user = userResult.rows[0];

    // Check if token is expired
    if (new Date() > user.email_verification_expires) {
      return res.status(400).json({
        error: 'Token expired',
        message: 'Email verification token has expired'
      });
    }

    // Update user as verified
    await query(`
      UPDATE users
      SET is_email_verified = true, email_verification_token = NULL, email_verification_expires = NULL
      WHERE id = $1
    `, [user.id]);

    res.json({
      message: 'Email verified successfully'
    });

  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({
      error: 'Email verification failed',
      message: 'Internal server error during email verification'
    });
  }
});

/**
 * @swagger
 * /auth/forgot-password:
 *   post:
 *     summary: Request password reset
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *     responses:
 *       200:
 *         description: Password reset email sent
 *       404:
 *         description: Email not found
 */
router.post('/forgot-password',
  sanitizeInput,
  validateSchema(schemas.passwordResetRequest),
  async (req, res) => {
    try {
      const { email } = req.body;

      // Check if user exists
      const userResult = await query('SELECT id FROM users WHERE email = $1', [email]);

      if (userResult.rows.length === 0) {
        // Don't reveal if email exists or not for security
        return res.json({
          message: 'If an account with that email exists, a password reset link has been sent'
        });
      }

      const userId = userResult.rows[0].id;

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

      // Store reset token
      await query(`
        UPDATE users
        SET password_reset_token = $1, password_reset_expires = $2
        WHERE id = $3
      `, [resetToken, resetExpires, userId]);

      // TODO: Send password reset email
      console.log(`Password reset token for ${email}: ${resetToken}`);

      res.json({
        message: 'If an account with that email exists, a password reset link has been sent'
      });

    } catch (error) {
      console.error('Password reset request error:', error);
      res.status(500).json({
        error: 'Password reset request failed',
        message: 'Internal server error during password reset request'
      });
    }
  }
);

/**
 * @swagger
 * /auth/reset-password:
 *   post:
 *     summary: Reset password
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *               - password
 *             properties:
 *               token:
 *                 type: string
 *               password:
 *                 type: string
 *                 minLength: 8
 *     responses:
 *       200:
 *         description: Password reset successfully
 *       400:
 *         description: Invalid or expired token
 */
router.post('/reset-password',
  sanitizeInput,
  validateSchema(schemas.passwordReset),
  async (req, res) => {
    try {
      const { token, password } = req.body;

      // Find user with this reset token
      const userResult = await query(`
        SELECT id, password_reset_expires
        FROM users
        WHERE password_reset_token = $1
      `, [token]);

      if (userResult.rows.length === 0) {
        return res.status(400).json({
          error: 'Invalid token',
          message: 'Password reset token is invalid'
        });
      }

      const user = userResult.rows[0];

      // Check if token is expired
      if (new Date() > user.password_reset_expires) {
        return res.status(400).json({
          error: 'Token expired',
          message: 'Password reset token has expired'
        });
      }

      // Hash new password
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      // Update password and clear reset token
      await query(`
        UPDATE users
        SET password_hash = $1, password_reset_token = NULL, password_reset_expires = NULL,
            login_attempts = 0, locked_until = NULL
        WHERE id = $2
      `, [passwordHash, user.id]);

      // Invalidate all existing sessions
      await query('DELETE FROM user_sessions WHERE user_id = $1', [user.id]);

      res.json({
        message: 'Password reset successfully'
      });

    } catch (error) {
      console.error('Password reset error:', error);
      res.status(500).json({
        error: 'Password reset failed',
        message: 'Internal server error during password reset'
      });
    }
  }
);

module.exports = router;
