const express = require('express');
const { query, transaction } = require('../config/database');
const { verifyToken, requireEmailVerification, checkResourceOwnership } = require('../middleware/auth');
const { validateSchema, schemas, sanitizeInput } = require('../middleware/validation');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Alert:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         description:
 *           type: string
 *         categoryId:
 *           type: string
 *           format: uuid
 *         dataSourceId:
 *           type: string
 *           format: uuid
 *         conditions:
 *           type: object
 *         notificationChannels:
 *           type: array
 *           items:
 *             type: string
 *         isActive:
 *           type: boolean
 *         priority:
 *           type: string
 *           enum: [low, medium, high, critical]
 *         frequencyMinutes:
 *           type: integer
 *         maxNotificationsPerDay:
 *           type: integer
 *         lastChecked:
 *           type: string
 *           format: date-time
 *         lastTriggered:
 *           type: string
 *           format: date-time
 *         triggerCount:
 *           type: integer
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /alerts:
 *   get:
 *     summary: Get user alerts
 *     tags: [Alerts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [low, medium, high, critical]
 *     responses:
 *       200:
 *         description: Alerts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 alerts:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Alert'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     pages:
 *                       type: integer
 */
router.get('/', verifyToken, requireEmailVerification, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      isActive,
      priority,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const offset = (page - 1) * limit;

    // Build WHERE clause
    const conditions = ['a.user_id = $1'];
    const values = [req.user.id];
    let paramCount = 2;

    if (category) {
      conditions.push(`ac.name = $${paramCount++}`);
      values.push(category);
    }

    if (isActive !== undefined) {
      conditions.push(`a.is_active = $${paramCount++}`);
      values.push(isActive === 'true');
    }

    if (priority) {
      conditions.push(`a.priority = $${paramCount++}`);
      values.push(priority);
    }

    const whereClause = conditions.join(' AND ');

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM alerts a
      LEFT JOIN alert_categories ac ON a.category_id = ac.id
      WHERE ${whereClause}
    `;

    const countResult = await query(countQuery, values);
    const total = parseInt(countResult.rows[0].total);

    // Get alerts
    const alertsQuery = `
      SELECT 
        a.id, a.name, a.description, a.category_id, a.data_source_id,
        a.conditions, a.notification_channels, a.is_active, a.priority,
        a.frequency_minutes, a.max_notifications_per_day, a.last_checked,
        a.last_triggered, a.trigger_count, a.ai_optimized, a.ai_suggestions,
        a.created_at, a.updated_at,
        ac.name as category_name, ac.icon as category_icon, ac.color as category_color,
        ds.name as data_source_name, ds.type as data_source_type
      FROM alerts a
      LEFT JOIN alert_categories ac ON a.category_id = ac.id
      LEFT JOIN data_sources ds ON a.data_source_id = ds.id
      WHERE ${whereClause}
      ORDER BY a.${sortBy} ${sortOrder.toUpperCase()}
      LIMIT $${paramCount++} OFFSET $${paramCount++}
    `;

    values.push(limit, offset);

    const alertsResult = await query(alertsQuery, values);

    const alerts = alertsResult.rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      categoryId: row.category_id,
      dataSourceId: row.data_source_id,
      conditions: row.conditions,
      notificationChannels: row.notification_channels,
      isActive: row.is_active,
      priority: row.priority,
      frequencyMinutes: row.frequency_minutes,
      maxNotificationsPerDay: row.max_notifications_per_day,
      lastChecked: row.last_checked,
      lastTriggered: row.last_triggered,
      triggerCount: row.trigger_count,
      aiOptimized: row.ai_optimized,
      aiSuggestions: row.ai_suggestions,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      category: row.category_name ? {
        name: row.category_name,
        icon: row.category_icon,
        color: row.category_color
      } : null,
      dataSource: {
        name: row.data_source_name,
        type: row.data_source_type
      }
    }));

    res.json({
      alerts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get alerts error:', error);
    res.status(500).json({
      error: 'Failed to get alerts',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /alerts:
 *   post:
 *     summary: Create a new alert
 *     tags: [Alerts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - dataSourceId
 *               - conditions
 *               - notificationChannels
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               categoryId:
 *                 type: string
 *                 format: uuid
 *               dataSourceId:
 *                 type: string
 *                 format: uuid
 *               conditions:
 *                 type: object
 *               notificationChannels:
 *                 type: array
 *                 items:
 *                   type: string
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high, critical]
 *               frequencyMinutes:
 *                 type: integer
 *               maxNotificationsPerDay:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Alert created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Alert'
 *       400:
 *         description: Validation error
 *       403:
 *         description: Alert limit exceeded
 */
router.post('/',
  verifyToken,
  requireEmailVerification,
  sanitizeInput,
  validateSchema(schemas.alertCreation),
  async (req, res) => {
    try {
      const {
        name,
        description,
        categoryId,
        dataSourceId,
        conditions,
        notificationChannels,
        priority = 'medium',
        frequencyMinutes = 5,
        maxNotificationsPerDay = 10
      } = req.body;

      // Check user's alert limit
      const alertCountResult = await query(
        'SELECT COUNT(*) as count FROM alerts WHERE user_id = $1',
        [req.user.id]
      );

      const maxAlerts = parseInt(process.env.MAX_ALERTS_PER_USER) || 50;
      if (parseInt(alertCountResult.rows[0].count) >= maxAlerts) {
        return res.status(403).json({
          error: 'Alert limit exceeded',
          message: `You can create a maximum of ${maxAlerts} alerts`
        });
      }

      // Verify data source exists and is active
      const dataSourceResult = await query(
        'SELECT id, name FROM data_sources WHERE id = $1 AND is_active = true',
        [dataSourceId]
      );

      if (dataSourceResult.rows.length === 0) {
        return res.status(400).json({
          error: 'Invalid data source',
          message: 'Data source not found or inactive'
        });
      }

      // Verify category exists if provided
      if (categoryId) {
        const categoryResult = await query(
          'SELECT id FROM alert_categories WHERE id = $1',
          [categoryId]
        );

        if (categoryResult.rows.length === 0) {
          return res.status(400).json({
            error: 'Invalid category',
            message: 'Alert category not found'
          });
        }
      }

      // Create alert
      const alertResult = await query(`
        INSERT INTO alerts (
          user_id, name, description, category_id, data_source_id,
          conditions, notification_channels, priority, frequency_minutes,
          max_notifications_per_day
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      `, [
        req.user.id,
        name,
        description,
        categoryId,
        dataSourceId,
        JSON.stringify(conditions),
        JSON.stringify(notificationChannels),
        priority,
        frequencyMinutes,
        maxNotificationsPerDay
      ]);

      const alert = alertResult.rows[0];

      res.status(201).json({
        id: alert.id,
        name: alert.name,
        description: alert.description,
        categoryId: alert.category_id,
        dataSourceId: alert.data_source_id,
        conditions: alert.conditions,
        notificationChannels: alert.notification_channels,
        isActive: alert.is_active,
        priority: alert.priority,
        frequencyMinutes: alert.frequency_minutes,
        maxNotificationsPerDay: alert.max_notifications_per_day,
        lastChecked: alert.last_checked,
        lastTriggered: alert.last_triggered,
        triggerCount: alert.trigger_count,
        aiOptimized: alert.ai_optimized,
        aiSuggestions: alert.ai_suggestions,
        createdAt: alert.created_at,
        updatedAt: alert.updated_at
      });

    } catch (error) {
      console.error('Create alert error:', error);
      res.status(500).json({
        error: 'Failed to create alert',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @swagger
 * /alerts/{id}:
 *   get:
 *     summary: Get alert by ID
 *     tags: [Alerts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Alert retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Alert'
 *       404:
 *         description: Alert not found
 */
router.get('/:id', verifyToken, requireEmailVerification, async (req, res) => {
  try {
    const { id } = req.params;

    const alertResult = await query(`
      SELECT
        a.id, a.name, a.description, a.category_id, a.data_source_id,
        a.conditions, a.notification_channels, a.is_active, a.priority,
        a.frequency_minutes, a.max_notifications_per_day, a.last_checked,
        a.last_triggered, a.trigger_count, a.ai_optimized, a.ai_suggestions,
        a.created_at, a.updated_at,
        ac.name as category_name, ac.icon as category_icon, ac.color as category_color,
        ds.name as data_source_name, ds.type as data_source_type
      FROM alerts a
      LEFT JOIN alert_categories ac ON a.category_id = ac.id
      LEFT JOIN data_sources ds ON a.data_source_id = ds.id
      WHERE a.id = $1 AND a.user_id = $2
    `, [id, req.user.id]);

    if (alertResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Alert not found',
        message: 'Alert not found or you do not have permission to access it'
      });
    }

    const row = alertResult.rows[0];

    res.json({
      id: row.id,
      name: row.name,
      description: row.description,
      categoryId: row.category_id,
      dataSourceId: row.data_source_id,
      conditions: row.conditions,
      notificationChannels: row.notification_channels,
      isActive: row.is_active,
      priority: row.priority,
      frequencyMinutes: row.frequency_minutes,
      maxNotificationsPerDay: row.max_notifications_per_day,
      lastChecked: row.last_checked,
      lastTriggered: row.last_triggered,
      triggerCount: row.trigger_count,
      aiOptimized: row.ai_optimized,
      aiSuggestions: row.ai_suggestions,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      category: row.category_name ? {
        name: row.category_name,
        icon: row.category_icon,
        color: row.category_color
      } : null,
      dataSource: {
        name: row.data_source_name,
        type: row.data_source_type
      }
    });

  } catch (error) {
    console.error('Get alert error:', error);
    res.status(500).json({
      error: 'Failed to get alert',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /alerts/{id}:
 *   put:
 *     summary: Update alert
 *     tags: [Alerts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               categoryId:
 *                 type: string
 *                 format: uuid
 *               conditions:
 *                 type: object
 *               notificationChannels:
 *                 type: array
 *                 items:
 *                   type: string
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high, critical]
 *               frequencyMinutes:
 *                 type: integer
 *               maxNotificationsPerDay:
 *                 type: integer
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Alert updated successfully
 *       404:
 *         description: Alert not found
 */
router.put('/:id',
  verifyToken,
  requireEmailVerification,
  sanitizeInput,
  validateSchema(schemas.alertUpdate),
  async (req, res) => {
    try {
      const { id } = req.params;
      const updates = req.body;

      // Check if alert exists and belongs to user
      const existingAlert = await query(
        'SELECT id FROM alerts WHERE id = $1 AND user_id = $2',
        [id, req.user.id]
      );

      if (existingAlert.rows.length === 0) {
        return res.status(404).json({
          error: 'Alert not found',
          message: 'Alert not found or you do not have permission to update it'
        });
      }

      // Verify category exists if provided
      if (updates.categoryId) {
        const categoryResult = await query(
          'SELECT id FROM alert_categories WHERE id = $1',
          [updates.categoryId]
        );

        if (categoryResult.rows.length === 0) {
          return res.status(400).json({
            error: 'Invalid category',
            message: 'Alert category not found'
          });
        }
      }

      // Build update query dynamically
      const updateFields = [];
      const values = [];
      let paramCount = 1;

      Object.keys(updates).forEach(key => {
        if (updates[key] !== undefined) {
          switch (key) {
            case 'name':
              updateFields.push(`name = $${paramCount++}`);
              values.push(updates[key]);
              break;
            case 'description':
              updateFields.push(`description = $${paramCount++}`);
              values.push(updates[key]);
              break;
            case 'categoryId':
              updateFields.push(`category_id = $${paramCount++}`);
              values.push(updates[key]);
              break;
            case 'conditions':
              updateFields.push(`conditions = $${paramCount++}`);
              values.push(JSON.stringify(updates[key]));
              break;
            case 'notificationChannels':
              updateFields.push(`notification_channels = $${paramCount++}`);
              values.push(JSON.stringify(updates[key]));
              break;
            case 'priority':
              updateFields.push(`priority = $${paramCount++}`);
              values.push(updates[key]);
              break;
            case 'frequencyMinutes':
              updateFields.push(`frequency_minutes = $${paramCount++}`);
              values.push(updates[key]);
              break;
            case 'maxNotificationsPerDay':
              updateFields.push(`max_notifications_per_day = $${paramCount++}`);
              values.push(updates[key]);
              break;
            case 'isActive':
              updateFields.push(`is_active = $${paramCount++}`);
              values.push(updates[key]);
              break;
          }
        }
      });

      if (updateFields.length === 0) {
        return res.status(400).json({
          error: 'No updates provided',
          message: 'At least one field must be provided for update'
        });
      }

      values.push(id);

      const updateQuery = `
        UPDATE alerts
        SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE id = $${paramCount}
        RETURNING *
      `;

      const result = await query(updateQuery, values);
      const alert = result.rows[0];

      res.json({
        id: alert.id,
        name: alert.name,
        description: alert.description,
        categoryId: alert.category_id,
        dataSourceId: alert.data_source_id,
        conditions: alert.conditions,
        notificationChannels: alert.notification_channels,
        isActive: alert.is_active,
        priority: alert.priority,
        frequencyMinutes: alert.frequency_minutes,
        maxNotificationsPerDay: alert.max_notifications_per_day,
        lastChecked: alert.last_checked,
        lastTriggered: alert.last_triggered,
        triggerCount: alert.trigger_count,
        aiOptimized: alert.ai_optimized,
        aiSuggestions: alert.ai_suggestions,
        createdAt: alert.created_at,
        updatedAt: alert.updated_at
      });

    } catch (error) {
      console.error('Update alert error:', error);
      res.status(500).json({
        error: 'Failed to update alert',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @swagger
 * /alerts/{id}:
 *   delete:
 *     summary: Delete alert
 *     tags: [Alerts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Alert deleted successfully
 *       404:
 *         description: Alert not found
 */
router.delete('/:id', verifyToken, requireEmailVerification, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(
      'DELETE FROM alerts WHERE id = $1 AND user_id = $2',
      [id, req.user.id]
    );

    if (result.rowCount === 0) {
      return res.status(404).json({
        error: 'Alert not found',
        message: 'Alert not found or you do not have permission to delete it'
      });
    }

    res.json({
      message: 'Alert deleted successfully'
    });

  } catch (error) {
    console.error('Delete alert error:', error);
    res.status(500).json({
      error: 'Failed to delete alert',
      message: 'Internal server error'
    });
  }
});

module.exports = router;
