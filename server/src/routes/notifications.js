const express = require('express');
const { query } = require('../config/database');
const { verifyToken, requireEmailVerification } = require('../middleware/auth');
const { validateSchema, schemas, sanitizeInput } = require('../middleware/validation');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Notification:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         alertId:
 *           type: string
 *           format: uuid
 *         type:
 *           type: string
 *           enum: [email, push, sms]
 *         channel:
 *           type: string
 *         subject:
 *           type: string
 *         content:
 *           type: string
 *         status:
 *           type: string
 *           enum: [pending, sent, failed, delivered]
 *         sentAt:
 *           type: string
 *           format: date-time
 *         deliveredAt:
 *           type: string
 *           format: date-time
 *         errorMessage:
 *           type: string
 *         retryCount:
 *           type: integer
 *         createdAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /notifications:
 *   get:
 *     summary: Get user notifications
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [email, push, sms]
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, sent, failed, delivered]
 *       - in: query
 *         name: alertId
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Notifications retrieved successfully
 */
router.get('/', verifyToken, requireEmailVerification, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      type,
      status,
      alertId,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const offset = (page - 1) * limit;

    // Build WHERE clause
    const conditions = ['n.user_id = $1'];
    const values = [req.user.id];
    let paramCount = 2;

    if (type) {
      conditions.push(`n.type = $${paramCount++}`);
      values.push(type);
    }

    if (status) {
      conditions.push(`n.status = $${paramCount++}`);
      values.push(status);
    }

    if (alertId) {
      conditions.push(`n.alert_id = $${paramCount++}`);
      values.push(alertId);
    }

    const whereClause = conditions.join(' AND ');

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM notifications n
      WHERE ${whereClause}
    `;

    const countResult = await query(countQuery, values);
    const total = parseInt(countResult.rows[0].total);

    // Get notifications
    const notificationsQuery = `
      SELECT 
        n.id, n.alert_id, n.type, n.channel, n.subject, n.content,
        n.status, n.sent_at, n.delivered_at, n.error_message, n.retry_count,
        n.metadata, n.created_at,
        a.name as alert_name
      FROM notifications n
      LEFT JOIN alerts a ON n.alert_id = a.id
      WHERE ${whereClause}
      ORDER BY n.${sortBy} ${sortOrder.toUpperCase()}
      LIMIT $${paramCount++} OFFSET $${paramCount++}
    `;

    values.push(limit, offset);

    const notificationsResult = await query(notificationsQuery, values);

    const notifications = notificationsResult.rows.map(row => ({
      id: row.id,
      alertId: row.alert_id,
      alertName: row.alert_name,
      type: row.type,
      channel: row.channel,
      subject: row.subject,
      content: row.content,
      status: row.status,
      sentAt: row.sent_at,
      deliveredAt: row.delivered_at,
      errorMessage: row.error_message,
      retryCount: row.retry_count,
      metadata: row.metadata,
      createdAt: row.created_at
    }));

    res.json({
      notifications,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({
      error: 'Failed to get notifications',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /notifications/{id}:
 *   get:
 *     summary: Get notification by ID
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Notification retrieved successfully
 *       404:
 *         description: Notification not found
 */
router.get('/:id', verifyToken, requireEmailVerification, async (req, res) => {
  try {
    const { id } = req.params;

    const notificationResult = await query(`
      SELECT 
        n.id, n.alert_id, n.type, n.channel, n.subject, n.content,
        n.status, n.sent_at, n.delivered_at, n.error_message, n.retry_count,
        n.metadata, n.created_at,
        a.name as alert_name
      FROM notifications n
      LEFT JOIN alerts a ON n.alert_id = a.id
      WHERE n.id = $1 AND n.user_id = $2
    `, [id, req.user.id]);

    if (notificationResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Notification not found',
        message: 'Notification not found or you do not have permission to access it'
      });
    }

    const row = notificationResult.rows[0];

    res.json({
      id: row.id,
      alertId: row.alert_id,
      alertName: row.alert_name,
      type: row.type,
      channel: row.channel,
      subject: row.subject,
      content: row.content,
      status: row.status,
      sentAt: row.sent_at,
      deliveredAt: row.delivered_at,
      errorMessage: row.error_message,
      retryCount: row.retry_count,
      metadata: row.metadata,
      createdAt: row.created_at
    });

  } catch (error) {
    console.error('Get notification error:', error);
    res.status(500).json({
      error: 'Failed to get notification',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /notifications/push/subscribe:
 *   post:
 *     summary: Subscribe to push notifications
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - endpoint
 *               - keys
 *             properties:
 *               endpoint:
 *                 type: string
 *               keys:
 *                 type: object
 *                 properties:
 *                   p256dh:
 *                     type: string
 *                   auth:
 *                     type: string
 *               deviceInfo:
 *                 type: object
 *     responses:
 *       200:
 *         description: Push subscription created successfully
 *       400:
 *         description: Invalid subscription data
 */
router.post('/push/subscribe',
  verifyToken,
  requireEmailVerification,
  sanitizeInput,
  validateSchema(schemas.pushSubscription),
  async (req, res) => {
    try {
      const { endpoint, keys, deviceInfo } = req.body;

      // Check if subscription already exists
      const existingSubscription = await query(
        'SELECT id FROM push_subscriptions WHERE user_id = $1 AND endpoint = $2',
        [req.user.id, endpoint]
      );

      if (existingSubscription.rows.length > 0) {
        // Update existing subscription
        await query(`
          UPDATE push_subscriptions 
          SET p256dh_key = $1, auth_key = $2, device_info = $3, is_active = true
          WHERE user_id = $4 AND endpoint = $5
        `, [keys.p256dh, keys.auth, JSON.stringify(deviceInfo || {}), req.user.id, endpoint]);
      } else {
        // Create new subscription
        await query(`
          INSERT INTO push_subscriptions (user_id, endpoint, p256dh_key, auth_key, device_info)
          VALUES ($1, $2, $3, $4, $5)
        `, [req.user.id, endpoint, keys.p256dh, keys.auth, JSON.stringify(deviceInfo || {})]);
      }

      res.json({
        message: 'Push subscription created successfully'
      });

    } catch (error) {
      console.error('Push subscribe error:', error);
      res.status(500).json({
        error: 'Failed to create push subscription',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @swagger
 * /notifications/push/unsubscribe:
 *   post:
 *     summary: Unsubscribe from push notifications
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - endpoint
 *             properties:
 *               endpoint:
 *                 type: string
 *     responses:
 *       200:
 *         description: Push subscription removed successfully
 */
router.post('/push/unsubscribe', verifyToken, async (req, res) => {
  try {
    const { endpoint } = req.body;

    if (!endpoint) {
      return res.status(400).json({
        error: 'Endpoint required',
        message: 'Push notification endpoint is required'
      });
    }

    await query(
      'UPDATE push_subscriptions SET is_active = false WHERE user_id = $1 AND endpoint = $2',
      [req.user.id, endpoint]
    );

    res.json({
      message: 'Push subscription removed successfully'
    });

  } catch (error) {
    console.error('Push unsubscribe error:', error);
    res.status(500).json({
      error: 'Failed to remove push subscription',
      message: 'Internal server error'
    });
  }
});

module.exports = router;
