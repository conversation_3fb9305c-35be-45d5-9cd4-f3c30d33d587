const express = require('express');
const { query } = require('../config/database');
const { verifyToken, requireEmailVerification } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     DataSource:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         type:
 *           type: string
 *         apiEndpoint:
 *           type: string
 *         rateLimitPerHour:
 *           type: integer
 *         isActive:
 *           type: boolean
 *         configuration:
 *           type: object
 *         createdAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /data-sources:
 *   get:
 *     summary: Get available data sources
 *     tags: [Data Sources]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [stock, weather, news, custom]
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *     responses:
 *       200:
 *         description: Data sources retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 dataSources:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/DataSource'
 */
router.get('/', verifyToken, requireEmailVerification, async (req, res) => {
  try {
    const { type, isActive } = req.query;

    // Build WHERE clause
    const conditions = [];
    const values = [];
    let paramCount = 1;

    if (type) {
      conditions.push(`type = $${paramCount++}`);
      values.push(type);
    }

    if (isActive !== undefined) {
      conditions.push(`is_active = $${paramCount++}`);
      values.push(isActive === 'true');
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    const dataSourcesQuery = `
      SELECT id, name, type, api_endpoint, rate_limit_per_hour, is_active, configuration, created_at
      FROM data_sources
      ${whereClause}
      ORDER BY name
    `;

    const dataSourcesResult = await query(dataSourcesQuery, values);

    const dataSources = dataSourcesResult.rows.map(row => ({
      id: row.id,
      name: row.name,
      type: row.type,
      apiEndpoint: row.api_endpoint,
      rateLimitPerHour: row.rate_limit_per_hour,
      isActive: row.is_active,
      configuration: row.configuration,
      createdAt: row.created_at
    }));

    res.json({ dataSources });

  } catch (error) {
    console.error('Get data sources error:', error);
    res.status(500).json({
      error: 'Failed to get data sources',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /data-sources/{id}:
 *   get:
 *     summary: Get data source by ID
 *     tags: [Data Sources]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Data source retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DataSource'
 *       404:
 *         description: Data source not found
 */
router.get('/:id', verifyToken, requireEmailVerification, async (req, res) => {
  try {
    const { id } = req.params;

    const dataSourceResult = await query(`
      SELECT id, name, type, api_endpoint, rate_limit_per_hour, is_active, configuration, created_at
      FROM data_sources
      WHERE id = $1
    `, [id]);

    if (dataSourceResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Data source not found',
        message: 'Data source not found'
      });
    }

    const row = dataSourceResult.rows[0];

    res.json({
      id: row.id,
      name: row.name,
      type: row.type,
      apiEndpoint: row.api_endpoint,
      rateLimitPerHour: row.rate_limit_per_hour,
      isActive: row.is_active,
      configuration: row.configuration,
      createdAt: row.created_at
    });

  } catch (error) {
    console.error('Get data source error:', error);
    res.status(500).json({
      error: 'Failed to get data source',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /data-sources/categories:
 *   get:
 *     summary: Get alert categories
 *     tags: [Data Sources]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Categories retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 categories:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       icon:
 *                         type: string
 *                       color:
 *                         type: string
 */
router.get('/categories', verifyToken, requireEmailVerification, async (req, res) => {
  try {
    const categoriesResult = await query(`
      SELECT id, name, description, icon, color, created_at
      FROM alert_categories
      ORDER BY name
    `);

    const categories = categoriesResult.rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      icon: row.icon,
      color: row.color,
      createdAt: row.created_at
    }));

    res.json({ categories });

  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      error: 'Failed to get categories',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /data-sources/{type}/conditions:
 *   get:
 *     summary: Get sample conditions for data source type
 *     tags: [Data Sources]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: type
 *         required: true
 *         schema:
 *           type: string
 *           enum: [stock, weather, news]
 *     responses:
 *       200:
 *         description: Sample conditions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 conditions:
 *                   type: array
 *                   items:
 *                     type: object
 */
router.get('/:type/conditions', verifyToken, requireEmailVerification, async (req, res) => {
  try {
    const { type } = req.params;

    const validTypes = ['stock', 'weather', 'news'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({
        error: 'Invalid type',
        message: 'Type must be one of: stock, weather, news'
      });
    }

    const settingKey = `sample_${type}_conditions`;

    const settingResult = await query(
      'SELECT value FROM system_settings WHERE key = $1',
      [settingKey]
    );

    if (settingResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Conditions not found',
        message: `Sample conditions for ${type} not found`
      });
    }

    const conditions = JSON.parse(settingResult.rows[0].value);

    res.json({ conditions });

  } catch (error) {
    console.error('Get conditions error:', error);
    res.status(500).json({
      error: 'Failed to get conditions',
      message: 'Internal server error'
    });
  }
});

module.exports = router;
