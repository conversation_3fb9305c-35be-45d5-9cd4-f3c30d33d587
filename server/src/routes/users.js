const express = require('express');
const bcrypt = require('bcryptjs');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const { query } = require('../config/database');
const { verifyToken, requireEmailVerification } = require('../middleware/auth');
const { validateSchema, schemas, sanitizeInput } = require('../middleware/validation');

const router = express.Router();

/**
 * @swagger
 * /users/profile:
 *   get:
 *     summary: Get user profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized
 */
router.get('/profile', verifyToken, async (req, res) => {
  try {
    const userResult = await query(`
      SELECT id, email, first_name, last_name, is_email_verified, mfa_enabled, 
             last_login, created_at, updated_at
      FROM users 
      WHERE id = $1
    `, [req.user.id]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User profile not found'
      });
    }

    const user = userResult.rows[0];

    res.json({
      id: user.id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      isEmailVerified: user.is_email_verified,
      mfaEnabled: user.mfa_enabled,
      lastLogin: user.last_login,
      createdAt: user.created_at,
      updatedAt: user.updated_at
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      error: 'Failed to get profile',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /users/profile:
 *   put:
 *     summary: Update user profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *       400:
 *         description: Validation error
 *       409:
 *         description: Email already exists
 */
router.put('/profile', 
  verifyToken,
  sanitizeInput,
  validateSchema(schemas.userProfileUpdate),
  async (req, res) => {
    try {
      const { firstName, lastName, email } = req.body;
      const userId = req.user.id;

      // If email is being changed, check if it already exists
      if (email && email !== req.user.email) {
        const existingUser = await query(
          'SELECT id FROM users WHERE email = $1 AND id != $2',
          [email, userId]
        );

        if (existingUser.rows.length > 0) {
          return res.status(409).json({
            error: 'Email already exists',
            message: 'A user with this email address already exists'
          });
        }
      }

      // Build update query dynamically
      const updates = [];
      const values = [];
      let paramCount = 1;

      if (firstName !== undefined) {
        updates.push(`first_name = $${paramCount++}`);
        values.push(firstName);
      }

      if (lastName !== undefined) {
        updates.push(`last_name = $${paramCount++}`);
        values.push(lastName);
      }

      if (email !== undefined) {
        updates.push(`email = $${paramCount++}`);
        values.push(email);
        // Reset email verification if email changed
        if (email !== req.user.email) {
          updates.push(`is_email_verified = false`);
        }
      }

      if (updates.length === 0) {
        return res.status(400).json({
          error: 'No updates provided',
          message: 'At least one field must be provided for update'
        });
      }

      values.push(userId);

      const updateQuery = `
        UPDATE users 
        SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE id = $${paramCount}
        RETURNING id, email, first_name, last_name, is_email_verified, mfa_enabled, updated_at
      `;

      const result = await query(updateQuery, values);
      const updatedUser = result.rows[0];

      res.json({
        id: updatedUser.id,
        email: updatedUser.email,
        firstName: updatedUser.first_name,
        lastName: updatedUser.last_name,
        isEmailVerified: updatedUser.is_email_verified,
        mfaEnabled: updatedUser.mfa_enabled,
        updatedAt: updatedUser.updated_at
      });

    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({
        error: 'Failed to update profile',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @swagger
 * /users/change-password:
 *   post:
 *     summary: Change user password
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *               newPassword:
 *                 type: string
 *                 minLength: 8
 *     responses:
 *       200:
 *         description: Password changed successfully
 *       400:
 *         description: Invalid current password
 */
router.post('/change-password',
  verifyToken,
  sanitizeInput,
  async (req, res) => {
    try {
      const { currentPassword, newPassword } = req.body;

      if (!currentPassword || !newPassword) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Current password and new password are required'
        });
      }

      // Validate new password
      const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;
      if (newPassword.length < 8 || !passwordRegex.test(newPassword)) {
        return res.status(400).json({
          error: 'Invalid password',
          message: 'Password must be at least 8 characters and contain uppercase, lowercase, number, and special character'
        });
      }

      // Get current password hash
      const userResult = await query(
        'SELECT password_hash FROM users WHERE id = $1',
        [req.user.id]
      );

      if (userResult.rows.length === 0) {
        return res.status(404).json({
          error: 'User not found',
          message: 'User not found'
        });
      }

      const user = userResult.rows[0];

      // Verify current password
      const passwordValid = await bcrypt.compare(currentPassword, user.password_hash);
      if (!passwordValid) {
        return res.status(400).json({
          error: 'Invalid current password',
          message: 'Current password is incorrect'
        });
      }

      // Hash new password
      const saltRounds = 12;
      const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

      // Update password
      await query(
        'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [newPasswordHash, req.user.id]
      );

      // Invalidate all sessions except current one (optional)
      // await query('DELETE FROM user_sessions WHERE user_id = $1', [req.user.id]);

      res.json({
        message: 'Password changed successfully'
      });

    } catch (error) {
      console.error('Change password error:', error);
      res.status(500).json({
        error: 'Failed to change password',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @swagger
 * /users/mfa/setup:
 *   post:
 *     summary: Setup MFA (TOTP)
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: MFA setup initiated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 secret:
 *                   type: string
 *                 qrCode:
 *                   type: string
 *                 backupCodes:
 *                   type: array
 *                   items:
 *                     type: string
 */
router.post('/mfa/setup', verifyToken, requireEmailVerification, async (req, res) => {
  try {
    // Check if MFA is already enabled
    const userResult = await query(
      'SELECT mfa_enabled FROM users WHERE id = $1',
      [req.user.id]
    );

    if (userResult.rows[0].mfa_enabled) {
      return res.status(400).json({
        error: 'MFA already enabled',
        message: 'Multi-factor authentication is already enabled for this account'
      });
    }

    // Generate secret
    const secret = speakeasy.generateSecret({
      name: `${req.user.email}`,
      issuer: process.env.MFA_ISSUER || 'SMALT Alert System',
      length: 32
    });

    // Generate QR code
    const qrCode = await QRCode.toDataURL(secret.otpauth_url);

    // Generate backup codes
    const backupCodes = [];
    for (let i = 0; i < 10; i++) {
      backupCodes.push(Math.random().toString(36).substring(2, 10).toUpperCase());
    }

    // Store secret temporarily (not enabled yet)
    await query(
      'UPDATE users SET mfa_secret = $1, mfa_backup_codes = $2 WHERE id = $3',
      [secret.base32, backupCodes, req.user.id]
    );

    res.json({
      secret: secret.base32,
      qrCode,
      backupCodes
    });

  } catch (error) {
    console.error('MFA setup error:', error);
    res.status(500).json({
      error: 'Failed to setup MFA',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /users/mfa/verify:
 *   post:
 *     summary: Verify and enable MFA
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *                 description: TOTP token from authenticator app
 *     responses:
 *       200:
 *         description: MFA enabled successfully
 *       400:
 *         description: Invalid token
 */
router.post('/mfa/verify', verifyToken, async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        error: 'Token required',
        message: 'TOTP token is required'
      });
    }

    // Get user's MFA secret
    const userResult = await query(
      'SELECT mfa_secret, mfa_enabled FROM users WHERE id = $1',
      [req.user.id]
    );

    const user = userResult.rows[0];

    if (user.mfa_enabled) {
      return res.status(400).json({
        error: 'MFA already enabled',
        message: 'Multi-factor authentication is already enabled'
      });
    }

    if (!user.mfa_secret) {
      return res.status(400).json({
        error: 'MFA not setup',
        message: 'MFA setup must be initiated first'
      });
    }

    // Verify token
    const verified = speakeasy.totp.verify({
      secret: user.mfa_secret,
      encoding: 'base32',
      token: token,
      window: 2
    });

    if (!verified) {
      return res.status(400).json({
        error: 'Invalid token',
        message: 'TOTP token is invalid'
      });
    }

    // Enable MFA
    await query(
      'UPDATE users SET mfa_enabled = true WHERE id = $1',
      [req.user.id]
    );

    res.json({
      message: 'MFA enabled successfully'
    });

  } catch (error) {
    console.error('MFA verify error:', error);
    res.status(500).json({
      error: 'Failed to verify MFA',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /users/mfa/disable:
 *   post:
 *     summary: Disable MFA
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - password
 *             properties:
 *               password:
 *                 type: string
 *               token:
 *                 type: string
 *                 description: TOTP token or backup code
 *     responses:
 *       200:
 *         description: MFA disabled successfully
 *       400:
 *         description: Invalid credentials
 */
router.post('/mfa/disable', verifyToken, async (req, res) => {
  try {
    const { password, token } = req.body;

    if (!password) {
      return res.status(400).json({
        error: 'Password required',
        message: 'Password is required to disable MFA'
      });
    }

    // Get user data
    const userResult = await query(
      'SELECT password_hash, mfa_enabled, mfa_secret, mfa_backup_codes FROM users WHERE id = $1',
      [req.user.id]
    );

    const user = userResult.rows[0];

    if (!user.mfa_enabled) {
      return res.status(400).json({
        error: 'MFA not enabled',
        message: 'Multi-factor authentication is not enabled'
      });
    }

    // Verify password
    const passwordValid = await bcrypt.compare(password, user.password_hash);
    if (!passwordValid) {
      return res.status(400).json({
        error: 'Invalid password',
        message: 'Password is incorrect'
      });
    }

    // Verify MFA token if provided
    if (token) {
      let tokenValid = false;

      // Check if it's a TOTP token
      if (token.length === 6 && /^\d+$/.test(token)) {
        tokenValid = speakeasy.totp.verify({
          secret: user.mfa_secret,
          encoding: 'base32',
          token: token,
          window: 2
        });
      }
      // Check if it's a backup code
      else if (user.mfa_backup_codes && user.mfa_backup_codes.includes(token.toUpperCase())) {
        tokenValid = true;
        // Remove used backup code
        const updatedBackupCodes = user.mfa_backup_codes.filter(code => code !== token.toUpperCase());
        await query(
          'UPDATE users SET mfa_backup_codes = $1 WHERE id = $2',
          [updatedBackupCodes, req.user.id]
        );
      }

      if (!tokenValid) {
        return res.status(400).json({
          error: 'Invalid token',
          message: 'MFA token or backup code is invalid'
        });
      }
    }

    // Disable MFA
    await query(
      'UPDATE users SET mfa_enabled = false, mfa_secret = NULL, mfa_backup_codes = NULL WHERE id = $1',
      [req.user.id]
    );

    res.json({
      message: 'MFA disabled successfully'
    });

  } catch (error) {
    console.error('MFA disable error:', error);
    res.status(500).json({
      error: 'Failed to disable MFA',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /users/sessions:
 *   get:
 *     summary: Get user sessions
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Sessions retrieved successfully
 */
router.get('/sessions', verifyToken, async (req, res) => {
  try {
    const sessionsResult = await query(`
      SELECT id, device_info, ip_address, created_at, expires_at
      FROM user_sessions
      WHERE user_id = $1 AND expires_at > CURRENT_TIMESTAMP
      ORDER BY created_at DESC
    `, [req.user.id]);

    const sessions = sessionsResult.rows.map(session => ({
      id: session.id,
      deviceInfo: session.device_info,
      ipAddress: session.ip_address,
      createdAt: session.created_at,
      expiresAt: session.expires_at
    }));

    res.json({ sessions });

  } catch (error) {
    console.error('Get sessions error:', error);
    res.status(500).json({
      error: 'Failed to get sessions',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /users/sessions/{sessionId}:
 *   delete:
 *     summary: Revoke a session
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Session revoked successfully
 *       404:
 *         description: Session not found
 */
router.delete('/sessions/:sessionId', verifyToken, async (req, res) => {
  try {
    const { sessionId } = req.params;

    const result = await query(
      'DELETE FROM user_sessions WHERE id = $1 AND user_id = $2',
      [sessionId, req.user.id]
    );

    if (result.rowCount === 0) {
      return res.status(404).json({
        error: 'Session not found',
        message: 'Session not found or already expired'
      });
    }

    res.json({
      message: 'Session revoked successfully'
    });

  } catch (error) {
    console.error('Revoke session error:', error);
    res.status(500).json({
      error: 'Failed to revoke session',
      message: 'Internal server error'
    });
  }
});

module.exports = router;
