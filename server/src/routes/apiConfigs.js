const express = require('express');
const CryptoJS = require('crypto-js');
const { query } = require('../config/database');
const { verifyToken, requireEmailVerification } = require('../middleware/auth');
const { validateSchema, schemas, sanitizeInput } = require('../middleware/validation');

const router = express.Router();

// Encryption/Decryption functions
const encryptApiKey = (apiKey) => {
  return CryptoJS.AES.encrypt(apiKey, process.env.ENCRYPTION_KEY).toString();
};

const decryptApiKey = (encryptedApiKey) => {
  const bytes = CryptoJS.AES.decrypt(encryptedApiKey, process.env.ENCRYPTION_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};

/**
 * @swagger
 * components:
 *   schemas:
 *     ApiConfig:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         serviceName:
 *           type: string
 *           enum: [openai, yahoo_finance, openweather, newsapi]
 *         apiEndpoint:
 *           type: string
 *         rateLimitPerHour:
 *           type: integer
 *         isActive:
 *           type: boolean
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api-configs:
 *   get:
 *     summary: Get user API configurations
 *     tags: [API Configurations]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: API configurations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 configs:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ApiConfig'
 */
router.get('/', verifyToken, requireEmailVerification, async (req, res) => {
  try {
    const configsResult = await query(`
      SELECT id, service_name, api_endpoint, rate_limit_per_hour, is_active, created_at, updated_at
      FROM user_api_configs
      WHERE user_id = $1
      ORDER BY service_name
    `, [req.user.id]);

    const configs = configsResult.rows.map(row => ({
      id: row.id,
      serviceName: row.service_name,
      apiEndpoint: row.api_endpoint,
      rateLimitPerHour: row.rate_limit_per_hour,
      isActive: row.is_active,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      hasApiKey: true // Don't expose the actual key
    }));

    res.json({ configs });

  } catch (error) {
    console.error('Get API configs error:', error);
    res.status(500).json({
      error: 'Failed to get API configurations',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api-configs:
 *   post:
 *     summary: Create or update API configuration
 *     tags: [API Configurations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - serviceName
 *               - apiKey
 *             properties:
 *               serviceName:
 *                 type: string
 *                 enum: [openai, yahoo_finance, openweather, newsapi]
 *               apiKey:
 *                 type: string
 *               apiEndpoint:
 *                 type: string
 *               rateLimitPerHour:
 *                 type: integer
 *     responses:
 *       200:
 *         description: API configuration saved successfully
 *       400:
 *         description: Validation error
 */
router.post('/',
  verifyToken,
  requireEmailVerification,
  sanitizeInput,
  validateSchema(schemas.apiConfigCreation),
  async (req, res) => {
    try {
      const { serviceName, apiKey, apiEndpoint, rateLimitPerHour = 100 } = req.body;

      // Encrypt the API key
      const encryptedApiKey = encryptApiKey(apiKey);

      // Check if configuration already exists
      const existingConfig = await query(
        'SELECT id FROM user_api_configs WHERE user_id = $1 AND service_name = $2',
        [req.user.id, serviceName]
      );

      let result;
      if (existingConfig.rows.length > 0) {
        // Update existing configuration
        result = await query(`
          UPDATE user_api_configs 
          SET api_key_encrypted = $1, api_endpoint = $2, rate_limit_per_hour = $3, 
              is_active = true, updated_at = CURRENT_TIMESTAMP
          WHERE user_id = $4 AND service_name = $5
          RETURNING id, service_name, api_endpoint, rate_limit_per_hour, is_active, created_at, updated_at
        `, [encryptedApiKey, apiEndpoint, rateLimitPerHour, req.user.id, serviceName]);
      } else {
        // Create new configuration
        result = await query(`
          INSERT INTO user_api_configs (user_id, service_name, api_key_encrypted, api_endpoint, rate_limit_per_hour)
          VALUES ($1, $2, $3, $4, $5)
          RETURNING id, service_name, api_endpoint, rate_limit_per_hour, is_active, created_at, updated_at
        `, [req.user.id, serviceName, encryptedApiKey, apiEndpoint, rateLimitPerHour]);
      }

      const config = result.rows[0];

      res.json({
        id: config.id,
        serviceName: config.service_name,
        apiEndpoint: config.api_endpoint,
        rateLimitPerHour: config.rate_limit_per_hour,
        isActive: config.is_active,
        createdAt: config.created_at,
        updatedAt: config.updated_at,
        message: 'API configuration saved successfully'
      });

    } catch (error) {
      console.error('Save API config error:', error);
      res.status(500).json({
        error: 'Failed to save API configuration',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @swagger
 * /api-configs/{serviceName}:
 *   get:
 *     summary: Get API configuration by service name
 *     tags: [API Configurations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: serviceName
 *         required: true
 *         schema:
 *           type: string
 *           enum: [openai, yahoo_finance, openweather, newsapi]
 *     responses:
 *       200:
 *         description: API configuration retrieved successfully
 *       404:
 *         description: Configuration not found
 */
router.get('/:serviceName', verifyToken, requireEmailVerification, async (req, res) => {
  try {
    const { serviceName } = req.params;

    const configResult = await query(`
      SELECT id, service_name, api_endpoint, rate_limit_per_hour, is_active, created_at, updated_at
      FROM user_api_configs
      WHERE user_id = $1 AND service_name = $2
    `, [req.user.id, serviceName]);

    if (configResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Configuration not found',
        message: `API configuration for ${serviceName} not found`
      });
    }

    const config = configResult.rows[0];

    res.json({
      id: config.id,
      serviceName: config.service_name,
      apiEndpoint: config.api_endpoint,
      rateLimitPerHour: config.rate_limit_per_hour,
      isActive: config.is_active,
      createdAt: config.created_at,
      updatedAt: config.updated_at,
      hasApiKey: true
    });

  } catch (error) {
    console.error('Get API config error:', error);
    res.status(500).json({
      error: 'Failed to get API configuration',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api-configs/{serviceName}:
 *   delete:
 *     summary: Delete API configuration
 *     tags: [API Configurations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: serviceName
 *         required: true
 *         schema:
 *           type: string
 *           enum: [openai, yahoo_finance, openweather, newsapi]
 *     responses:
 *       200:
 *         description: API configuration deleted successfully
 *       404:
 *         description: Configuration not found
 */
router.delete('/:serviceName', verifyToken, requireEmailVerification, async (req, res) => {
  try {
    const { serviceName } = req.params;

    const result = await query(
      'DELETE FROM user_api_configs WHERE user_id = $1 AND service_name = $2',
      [req.user.id, serviceName]
    );

    if (result.rowCount === 0) {
      return res.status(404).json({
        error: 'Configuration not found',
        message: `API configuration for ${serviceName} not found`
      });
    }

    res.json({
      message: 'API configuration deleted successfully'
    });

  } catch (error) {
    console.error('Delete API config error:', error);
    res.status(500).json({
      error: 'Failed to delete API configuration',
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api-configs/{serviceName}/test:
 *   post:
 *     summary: Test API configuration
 *     tags: [API Configurations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: serviceName
 *         required: true
 *         schema:
 *           type: string
 *           enum: [openai, yahoo_finance, openweather, newsapi]
 *     responses:
 *       200:
 *         description: API test successful
 *       400:
 *         description: API test failed
 *       404:
 *         description: Configuration not found
 */
router.post('/:serviceName/test', verifyToken, requireEmailVerification, async (req, res) => {
  try {
    const { serviceName } = req.params;

    // Get the API configuration
    const configResult = await query(`
      SELECT api_key_encrypted, api_endpoint
      FROM user_api_configs
      WHERE user_id = $1 AND service_name = $2 AND is_active = true
    `, [req.user.id, serviceName]);

    if (configResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Configuration not found',
        message: `API configuration for ${serviceName} not found or inactive`
      });
    }

    const config = configResult.rows[0];
    const apiKey = decryptApiKey(config.api_key_encrypted);

    // Test the API based on service type
    let testResult;
    switch (serviceName) {
      case 'openai':
        testResult = await testOpenAIAPI(apiKey, config.api_endpoint);
        break;
      case 'yahoo_finance':
        testResult = await testYahooFinanceAPI(apiKey);
        break;
      case 'openweather':
        testResult = await testOpenWeatherAPI(apiKey);
        break;
      case 'newsapi':
        testResult = await testNewsAPI(apiKey);
        break;
      default:
        return res.status(400).json({
          error: 'Unsupported service',
          message: `Testing for ${serviceName} is not supported`
        });
    }

    if (testResult.success) {
      res.json({
        success: true,
        message: 'API test successful',
        details: testResult.details
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'API test failed',
        error: testResult.error
      });
    }

  } catch (error) {
    console.error('Test API config error:', error);
    res.status(500).json({
      error: 'Failed to test API configuration',
      message: 'Internal server error'
    });
  }
});

// API testing functions (placeholder implementations)
async function testOpenAIAPI(apiKey, endpoint) {
  // TODO: Implement OpenAI API test
  return { success: true, details: 'OpenAI API test not implemented yet' };
}

async function testYahooFinanceAPI(apiKey) {
  // TODO: Implement Yahoo Finance API test
  return { success: true, details: 'Yahoo Finance API test not implemented yet' };
}

async function testOpenWeatherAPI(apiKey) {
  // TODO: Implement OpenWeather API test
  return { success: true, details: 'OpenWeather API test not implemented yet' };
}

async function testNewsAPI(apiKey) {
  // TODO: Implement News API test
  return { success: true, details: 'News API test not implemented yet' };
}

module.exports = router;
