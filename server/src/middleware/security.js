const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const { query } = require('../config/database');

// Enhanced rate limiting with user-specific limits
const createUserRateLimit = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    maxRequests = 100,
    skipSuccessfulRequests = false,
    skipFailedRequests = false
  } = options;

  return rateLimit({
    windowMs,
    max: maxRequests,
    skipSuccessfulRequests,
    skipFailedRequests,
    keyGenerator: (req) => {
      // Use user ID if authenticated, otherwise IP
      return req.user?.id || req.ip;
    },
    handler: (req, res) => {
      res.status(429).json({
        error: 'Rate limit exceeded',
        message: `Too many requests. Limit: ${maxRequests} per ${windowMs / 1000} seconds`,
        retryAfter: Math.ceil(windowMs / 1000)
      });
    },
    standardHeaders: true,
    legacyHeaders: false
  });
};

// Strict rate limiting for sensitive endpoints
const strictRateLimit = createUserRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5, // Only 5 attempts per 15 minutes
  skipSuccessfulRequests: true
});

// Login rate limiting
const loginRateLimit = createUserRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 10, // 10 login attempts per 15 minutes
  skipSuccessfulRequests: true
});

// API rate limiting
const apiRateLimit = createUserRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 1000 // 1000 API calls per 15 minutes
});

// Security headers middleware
const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.openai.com", "https://query1.finance.yahoo.com", "https://api.openweathermap.org", "https://newsapi.org"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      upgradeInsecureRequests: []
    }
  },
  crossOriginEmbedderPolicy: false, // Disable for API compatibility
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

// Input sanitization middleware
const sanitizeInput = (req, res, next) => {
  const sanitizeValue = (value) => {
    if (typeof value === 'string') {
      // Remove potential XSS attempts
      return value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .trim();
    }
    return value;
  };

  const sanitizeObject = (obj) => {
    if (obj && typeof obj === 'object') {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          if (Array.isArray(obj[key])) {
            obj[key] = obj[key].map(item => 
              typeof item === 'object' ? sanitizeObject(item) : sanitizeValue(item)
            );
          } else if (typeof obj[key] === 'object' && obj[key] !== null) {
            sanitizeObject(obj[key]);
          } else {
            obj[key] = sanitizeValue(obj[key]);
          }
        }
      }
    }
    return obj;
  };

  if (req.body) sanitizeObject(req.body);
  if (req.query) sanitizeObject(req.query);
  if (req.params) sanitizeObject(req.params);

  next();
};

// SQL injection prevention
const preventSQLInjection = (req, res, next) => {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /(;|\-\-|\#|\/\*|\*\/)/,
    /(\b(OR|AND)\b.*=.*)/i,
    /('|(\\')|('')|(%27)|(%2527))/i
  ];

  const checkValue = (value) => {
    if (typeof value === 'string') {
      return sqlPatterns.some(pattern => pattern.test(value));
    }
    return false;
  };

  const checkObject = (obj) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (Array.isArray(obj[key])) {
          if (obj[key].some(item => 
            typeof item === 'string' ? checkValue(item) : checkObject(item)
          )) {
            return true;
          }
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          if (checkObject(obj[key])) return true;
        } else if (checkValue(obj[key])) {
          return true;
        }
      }
    }
    return false;
  };

  // Check for SQL injection patterns
  const hasSQLInjection = [req.body, req.query, req.params]
    .some(obj => obj && checkObject(obj));

  if (hasSQLInjection) {
    return res.status(400).json({
      error: 'Invalid input',
      message: 'Request contains potentially harmful content'
    });
  }

  next();
};

// CSRF protection for state-changing operations
const csrfProtection = (req, res, next) => {
  // Skip CSRF for GET, HEAD, OPTIONS
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  // Check for CSRF token in header
  const csrfToken = req.headers['x-csrf-token'];
  const sessionToken = req.headers['x-session-token'];

  if (!csrfToken || !sessionToken) {
    return res.status(403).json({
      error: 'CSRF protection',
      message: 'CSRF token required for this operation'
    });
  }

  // Validate CSRF token (simplified - in production use crypto.timingSafeEqual)
  if (csrfToken !== sessionToken) {
    return res.status(403).json({
      error: 'CSRF protection',
      message: 'Invalid CSRF token'
    });
  }

  next();
};

// Audit logging middleware
const auditLog = (req, res, next) => {
  const originalSend = res.send;
  const startTime = Date.now();

  res.send = function(data) {
    const duration = Date.now() - startTime;
    
    // Log significant actions
    if (req.method !== 'GET' && req.user) {
      const logData = {
        userId: req.user.id,
        action: `${req.method} ${req.route?.path || req.path}`,
        resourceType: getResourceType(req.path),
        resourceId: req.params.id || null,
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
        statusCode: res.statusCode,
        duration,
        timestamp: new Date()
      };

      // Async logging (don't wait for it)
      logAuditEvent(logData).catch(err => 
        console.error('Audit logging failed:', err)
      );
    }

    originalSend.call(this, data);
  };

  next();
};

// Helper function to determine resource type from path
const getResourceType = (path) => {
  if (path.includes('/alerts')) return 'alert';
  if (path.includes('/users')) return 'user';
  if (path.includes('/notifications')) return 'notification';
  if (path.includes('/api-configs')) return 'api_config';
  return 'unknown';
};

// Async audit logging function
const logAuditEvent = async (logData) => {
  try {
    await query(`
      INSERT INTO audit_logs (
        user_id, action, resource_type, resource_id, 
        ip_address, user_agent, created_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [
      logData.userId,
      logData.action,
      logData.resourceType,
      logData.resourceId,
      logData.ipAddress,
      logData.userAgent,
      logData.timestamp
    ]);
  } catch (error) {
    console.error('Failed to log audit event:', error);
  }
};

// IP whitelist middleware (for admin endpoints)
const ipWhitelist = (allowedIPs = []) => {
  return (req, res, next) => {
    const clientIP = req.ip || req.connection.remoteAddress;
    
    if (allowedIPs.length > 0 && !allowedIPs.includes(clientIP)) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'IP address not allowed'
      });
    }
    
    next();
  };
};

// Request size limiting
const requestSizeLimit = (maxSize = '10mb') => {
  return (req, res, next) => {
    const contentLength = parseInt(req.headers['content-length'] || '0');
    const maxBytes = parseSize(maxSize);
    
    if (contentLength > maxBytes) {
      return res.status(413).json({
        error: 'Request too large',
        message: `Request size exceeds limit of ${maxSize}`
      });
    }
    
    next();
  };
};

// Helper function to parse size strings
const parseSize = (size) => {
  const units = { b: 1, kb: 1024, mb: 1024 * 1024, gb: 1024 * 1024 * 1024 };
  const match = size.toString().toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
  
  if (!match) return 0;
  
  const value = parseFloat(match[1]);
  const unit = match[2] || 'b';
  
  return Math.floor(value * units[unit]);
};

// Security monitoring middleware
const securityMonitoring = (req, res, next) => {
  // Monitor for suspicious patterns
  const suspiciousPatterns = [
    /\.\.\//g, // Directory traversal
    /%2e%2e%2f/gi, // Encoded directory traversal
    /\beval\s*\(/i, // Code injection
    /\bexec\s*\(/i, // Command injection
    /<iframe/i, // Iframe injection
    /<object/i, // Object injection
  ];

  const checkForSuspiciousActivity = (value) => {
    if (typeof value === 'string') {
      return suspiciousPatterns.some(pattern => pattern.test(value));
    }
    return false;
  };

  const requestString = JSON.stringify({
    url: req.url,
    headers: req.headers,
    body: req.body,
    query: req.query
  });

  if (checkForSuspiciousActivity(requestString)) {
    console.warn('Suspicious activity detected:', {
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      url: req.url,
      timestamp: new Date()
    });

    return res.status(400).json({
      error: 'Suspicious activity detected',
      message: 'Request blocked for security reasons'
    });
  }

  next();
};

module.exports = {
  securityHeaders,
  sanitizeInput,
  preventSQLInjection,
  csrfProtection,
  auditLog,
  ipWhitelist,
  requestSizeLimit,
  securityMonitoring,
  strictRateLimit,
  loginRateLimit,
  apiRateLimit,
  createUserRateLimit
};
