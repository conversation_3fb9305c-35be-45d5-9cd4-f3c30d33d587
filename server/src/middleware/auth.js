const jwt = require('jsonwebtoken');
const { query } = require('../config/database');

// Verify JWT token
const verifyToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'No token provided or invalid format'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Check if user still exists and is active
      const userResult = await query(
        'SELECT id, email, first_name, last_name, is_email_verified, mfa_enabled FROM users WHERE id = $1',
        [decoded.userId]
      );

      if (userResult.rows.length === 0) {
        return res.status(401).json({
          error: 'Access denied',
          message: 'User not found'
        });
      }

      const user = userResult.rows[0];
      
      // Attach user info to request
      req.user = {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        isEmailVerified: user.is_email_verified,
        mfaEnabled: user.mfa_enabled
      };

      next();
    } catch (jwtError) {
      if (jwtError.name === 'TokenExpiredError') {
        return res.status(401).json({
          error: 'Token expired',
          message: 'Please refresh your token'
        });
      } else if (jwtError.name === 'JsonWebTokenError') {
        return res.status(401).json({
          error: 'Invalid token',
          message: 'Token is malformed'
        });
      } else {
        throw jwtError;
      }
    }
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(500).json({
      error: 'Authentication error',
      message: 'Internal server error during authentication'
    });
  }
};

// Require email verification
const requireEmailVerification = (req, res, next) => {
  if (!req.user.isEmailVerified) {
    return res.status(403).json({
      error: 'Email verification required',
      message: 'Please verify your email address to access this resource'
    });
  }
  next();
};

// Require MFA if enabled
const requireMFA = async (req, res, next) => {
  try {
    if (req.user.mfaEnabled) {
      // Check if this request has valid MFA token
      const mfaToken = req.headers['x-mfa-token'];
      
      if (!mfaToken) {
        return res.status(403).json({
          error: 'MFA required',
          message: 'Multi-factor authentication token required'
        });
      }

      // Verify MFA token (implementation depends on MFA method)
      // This is a placeholder - actual implementation would verify TOTP/SMS
      const mfaValid = await verifyMFAToken(req.user.id, mfaToken);
      
      if (!mfaValid) {
        return res.status(403).json({
          error: 'Invalid MFA token',
          message: 'Multi-factor authentication failed'
        });
      }
    }
    
    next();
  } catch (error) {
    console.error('MFA middleware error:', error);
    return res.status(500).json({
      error: 'MFA verification error',
      message: 'Internal server error during MFA verification'
    });
  }
};

// Verify MFA token (placeholder implementation)
const verifyMFAToken = async (userId, token) => {
  // This would implement actual TOTP/SMS verification
  // For now, return true as placeholder
  return true;
};

// Optional authentication (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      req.user = null;
      return next();
    }

    const token = authHeader.substring(7);
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      const userResult = await query(
        'SELECT id, email, first_name, last_name, is_email_verified, mfa_enabled FROM users WHERE id = $1',
        [decoded.userId]
      );

      if (userResult.rows.length > 0) {
        const user = userResult.rows[0];
        req.user = {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          isEmailVerified: user.is_email_verified,
          mfaEnabled: user.mfa_enabled
        };
      } else {
        req.user = null;
      }
    } catch (jwtError) {
      req.user = null;
    }

    next();
  } catch (error) {
    console.error('Optional auth middleware error:', error);
    req.user = null;
    next();
  }
};

// Check if user owns resource
const checkResourceOwnership = (resourceIdParam = 'id', userIdField = 'user_id') => {
  return async (req, res, next) => {
    try {
      const resourceId = req.params[resourceIdParam];
      const userId = req.user.id;

      // This is a generic ownership check
      // Specific implementations should override this logic
      req.resourceOwnership = {
        resourceId,
        userId,
        userIdField
      };

      next();
    } catch (error) {
      console.error('Resource ownership check error:', error);
      return res.status(500).json({
        error: 'Authorization error',
        message: 'Internal server error during authorization'
      });
    }
  };
};

// Rate limiting per user
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const userRequests = new Map();

  return (req, res, next) => {
    if (!req.user) {
      return next();
    }

    const userId = req.user.id;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old entries
    if (userRequests.has(userId)) {
      const requests = userRequests.get(userId).filter(time => time > windowStart);
      userRequests.set(userId, requests);
    } else {
      userRequests.set(userId, []);
    }

    const userRequestCount = userRequests.get(userId).length;

    if (userRequestCount >= maxRequests) {
      return res.status(429).json({
        error: 'Rate limit exceeded',
        message: `Too many requests. Limit: ${maxRequests} per ${windowMs / 1000} seconds`
      });
    }

    // Add current request
    userRequests.get(userId).push(now);
    next();
  };
};

module.exports = {
  verifyToken,
  requireEmailVerification,
  requireMFA,
  optionalAuth,
  checkResourceOwnership,
  userRateLimit
};
