const { body, param, query, validationResult } = require('express-validator');
const Joi = require('joi');

// Express-validator error handler
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array().map(error => ({
        field: error.path,
        message: error.msg,
        value: error.value
      }))
    });
  }
  next();
};

// Joi validation middleware
const validateSchema = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }))
      });
    }

    req[property] = value;
    next();
  };
};

// Common validation schemas
const schemas = {
  // User registration
  userRegistration: Joi.object({
    email: Joi.string().email().required().max(255),
    password: Joi.string().min(8).max(128).required()
      .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .message('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    firstName: Joi.string().min(1).max(100).required(),
    lastName: Joi.string().min(1).max(100).required()
  }),

  // User login
  userLogin: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required(),
    mfaToken: Joi.string().optional()
  }),

  // Password reset request
  passwordResetRequest: Joi.object({
    email: Joi.string().email().required()
  }),

  // Password reset
  passwordReset: Joi.object({
    token: Joi.string().required(),
    password: Joi.string().min(8).max(128).required()
      .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
  }),

  // User profile update
  userProfileUpdate: Joi.object({
    firstName: Joi.string().min(1).max(100).optional(),
    lastName: Joi.string().min(1).max(100).optional(),
    email: Joi.string().email().max(255).optional()
  }),

  // Alert creation
  alertCreation: Joi.object({
    name: Joi.string().min(1).max(200).required(),
    description: Joi.string().max(1000).optional(),
    categoryId: Joi.string().uuid().optional(),
    dataSourceId: Joi.string().uuid().required(),
    conditions: Joi.object().required(),
    notificationChannels: Joi.array().items(
      Joi.string().valid('email', 'push', 'sms')
    ).min(1).required(),
    priority: Joi.string().valid('low', 'medium', 'high', 'critical').default('medium'),
    frequencyMinutes: Joi.number().integer().min(1).max(1440).default(5),
    maxNotificationsPerDay: Joi.number().integer().min(1).max(100).default(10)
  }),

  // Alert update
  alertUpdate: Joi.object({
    name: Joi.string().min(1).max(200).optional(),
    description: Joi.string().max(1000).optional(),
    categoryId: Joi.string().uuid().optional(),
    conditions: Joi.object().optional(),
    notificationChannels: Joi.array().items(
      Joi.string().valid('email', 'push', 'sms')
    ).min(1).optional(),
    priority: Joi.string().valid('low', 'medium', 'high', 'critical').optional(),
    frequencyMinutes: Joi.number().integer().min(1).max(1440).optional(),
    maxNotificationsPerDay: Joi.number().integer().min(1).max(100).optional(),
    isActive: Joi.boolean().optional()
  }),

  // API configuration
  apiConfigCreation: Joi.object({
    serviceName: Joi.string().valid('openai', 'yahoo_finance', 'openweather', 'newsapi').required(),
    apiKey: Joi.string().required(),
    apiEndpoint: Joi.string().uri().optional(),
    rateLimitPerHour: Joi.number().integer().min(1).max(10000).default(100)
  }),

  // Push subscription
  pushSubscription: Joi.object({
    endpoint: Joi.string().uri().required(),
    keys: Joi.object({
      p256dh: Joi.string().required(),
      auth: Joi.string().required()
    }).required(),
    deviceInfo: Joi.object().optional()
  }),

  // UUID parameter
  uuidParam: Joi.string().uuid().required(),

  // Pagination query
  paginationQuery: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  })
};

// Express-validator rules
const validationRules = {
  // User registration
  userRegistration: [
    body('email').isEmail().normalizeEmail().isLength({ max: 255 }),
    body('password').isLength({ min: 8, max: 128 })
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    body('firstName').trim().isLength({ min: 1, max: 100 }),
    body('lastName').trim().isLength({ min: 1, max: 100 })
  ],

  // User login
  userLogin: [
    body('email').isEmail().normalizeEmail(),
    body('password').notEmpty(),
    body('mfaToken').optional().isLength({ min: 6, max: 6 })
  ],

  // UUID parameter
  uuidParam: [
    param('id').isUUID().withMessage('Invalid UUID format')
  ],

  // Pagination query
  paginationQuery: [
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('sortBy').optional().isString(),
    query('sortOrder').optional().isIn(['asc', 'desc'])
  ]
};

// Sanitization middleware
const sanitizeInput = (req, res, next) => {
  // Remove any potential XSS attempts
  const sanitizeValue = (value) => {
    if (typeof value === 'string') {
      return value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    }
    return value;
  };

  const sanitizeObject = (obj) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          sanitizeObject(obj[key]);
        } else {
          obj[key] = sanitizeValue(obj[key]);
        }
      }
    }
  };

  if (req.body) sanitizeObject(req.body);
  if (req.query) sanitizeObject(req.query);
  if (req.params) sanitizeObject(req.params);

  next();
};

module.exports = {
  handleValidationErrors,
  validateSchema,
  schemas,
  validationRules,
  sanitizeInput
};
