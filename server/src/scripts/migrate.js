#!/usr/bin/env node

const { runMigrations, testConnection, close } = require('../config/database');

async function migrate() {
  console.log('Starting database migration...');
  
  try {
    // Test database connection
    const connected = await testConnection();
    if (!connected) {
      console.error('Failed to connect to database');
      process.exit(1);
    }

    // Run migrations
    await runMigrations();
    console.log('Database migration completed successfully');
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await close();
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  migrate();
}

module.exports = migrate;
