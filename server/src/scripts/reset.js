#!/usr/bin/env node

const { query, testConnection, close } = require('../config/database');
const migrate = require('./migrate');
const seed = require('./seed');

async function reset() {
  console.log('Starting database reset...');
  
  try {
    // Test database connection
    const connected = await testConnection();
    if (!connected) {
      console.error('Failed to connect to database');
      process.exit(1);
    }

    // Drop all tables (be careful!)
    console.log('Dropping all tables...');
    await query(`
      DROP SCHEMA public CASCADE;
      CREATE SCHEMA public;
      GRANT ALL ON SCHEMA public TO postgres;
      GRANT ALL ON SCHEMA public TO public;
    `);

    console.log('Database reset completed. Running migrations and seeds...');
    
    // Run migrations
    await migrate();
    
    // Run seeds
    await seed();
    
    console.log('Database reset, migration, and seeding completed successfully');
    
  } catch (error) {
    console.error('Database reset failed:', error);
    process.exit(1);
  } finally {
    await close();
  }
}

// Run reset if this script is executed directly
if (require.main === module) {
  // Add confirmation prompt for safety
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  rl.question('This will DELETE ALL DATA in the database. Are you sure? (yes/no): ', (answer) => {
    if (answer.toLowerCase() === 'yes') {
      reset();
    } else {
      console.log('Database reset cancelled');
      process.exit(0);
    }
    rl.close();
  });
} else {
  module.exports = reset;
}
