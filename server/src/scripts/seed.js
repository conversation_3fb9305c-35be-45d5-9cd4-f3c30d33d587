#!/usr/bin/env node

const { runSeeds, testConnection, close } = require('../config/database');

async function seed() {
  console.log('Starting database seeding...');
  
  try {
    // Test database connection
    const connected = await testConnection();
    if (!connected) {
      console.error('Failed to connect to database');
      process.exit(1);
    }

    // Run seeds
    await runSeeds();
    console.log('Database seeding completed successfully');
    
  } catch (error) {
    console.error('Seeding failed:', error);
    process.exit(1);
  } finally {
    await close();
  }
}

// Run seeding if this script is executed directly
if (require.main === module) {
  seed();
}

module.exports = seed;
