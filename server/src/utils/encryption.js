const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const CryptoJS = require('crypto-js');

// Encryption configuration
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY;
const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16; // For GCM, this is always 12, but we'll use 16 for compatibility
const SALT_LENGTH = 32;
const TAG_LENGTH = 16;

if (!ENCRYPTION_KEY || ENCRYPTION_KEY.length < 32) {
  throw new Error('ENCRYPTION_KEY must be at least 32 characters long');
}

// Password hashing utilities
class PasswordUtils {
  static async hash(password) {
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
  }

  static async verify(password, hash) {
    return await bcrypt.compare(password, hash);
  }

  static generateSecurePassword(length = 16) {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    
    // Ensure at least one character from each required type
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*';
    
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];
    
    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += charset[Math.floor(Math.random() * charset.length)];
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  static validatePasswordStrength(password) {
    const minLength = 8;
    const hasLowercase = /[a-z]/.test(password);
    const hasUppercase = /[A-Z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSymbols = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
    
    const score = [
      password.length >= minLength,
      hasLowercase,
      hasUppercase,
      hasNumbers,
      hasSymbols
    ].filter(Boolean).length;

    return {
      isValid: score >= 4,
      score,
      requirements: {
        minLength: password.length >= minLength,
        hasLowercase,
        hasUppercase,
        hasNumbers,
        hasSymbols
      }
    };
  }
}

// API Key encryption utilities
class ApiKeyEncryption {
  static encrypt(apiKey) {
    try {
      return CryptoJS.AES.encrypt(apiKey, ENCRYPTION_KEY).toString();
    } catch (error) {
      throw new Error('Failed to encrypt API key');
    }
  }

  static decrypt(encryptedApiKey) {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedApiKey, ENCRYPTION_KEY);
      const decrypted = bytes.toString(CryptoJS.enc.Utf8);
      
      if (!decrypted) {
        throw new Error('Failed to decrypt API key');
      }
      
      return decrypted;
    } catch (error) {
      throw new Error('Failed to decrypt API key');
    }
  }

  static isEncrypted(value) {
    try {
      // Try to decrypt - if it works, it's encrypted
      this.decrypt(value);
      return true;
    } catch {
      return false;
    }
  }
}

// Advanced encryption for sensitive data
class DataEncryption {
  static encrypt(data) {
    try {
      const iv = crypto.randomBytes(IV_LENGTH);
      const salt = crypto.randomBytes(SALT_LENGTH);
      
      // Derive key from password and salt
      const key = crypto.pbkdf2Sync(ENCRYPTION_KEY, salt, 100000, 32, 'sha256');
      
      const cipher = crypto.createCipher('aes-256-cbc', key);
      cipher.setAAD(salt); // Additional authenticated data
      
      let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const tag = cipher.getAuthTag();
      
      // Combine all components
      const result = {
        iv: iv.toString('hex'),
        salt: salt.toString('hex'),
        tag: tag.toString('hex'),
        data: encrypted
      };
      
      return Buffer.from(JSON.stringify(result)).toString('base64');
    } catch (error) {
      throw new Error('Failed to encrypt data');
    }
  }

  static decrypt(encryptedData) {
    try {
      const decoded = JSON.parse(Buffer.from(encryptedData, 'base64').toString());
      
      const iv = Buffer.from(decoded.iv, 'hex');
      const salt = Buffer.from(decoded.salt, 'hex');
      const tag = Buffer.from(decoded.tag, 'hex');
      const data = decoded.data;
      
      // Derive key from password and salt
      const key = crypto.pbkdf2Sync(ENCRYPTION_KEY, salt, 100000, 32, 'sha256');
      
      const decipher = crypto.createDecipher('aes-256-cbc', key);
      decipher.setAAD(salt);
      decipher.setAuthTag(tag);
      
      let decrypted = decipher.update(data, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return JSON.parse(decrypted);
    } catch (error) {
      throw new Error('Failed to decrypt data');
    }
  }
}

// Token utilities
class TokenUtils {
  static generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  static generateNumericToken(length = 6) {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  static hashToken(token) {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  static verifyToken(token, hash) {
    const tokenHash = this.hashToken(token);
    return crypto.timingSafeEqual(
      Buffer.from(tokenHash, 'hex'),
      Buffer.from(hash, 'hex')
    );
  }

  static generateCSRFToken() {
    return crypto.randomBytes(32).toString('base64');
  }
}

// Session utilities
class SessionUtils {
  static generateSessionId() {
    return crypto.randomBytes(32).toString('hex');
  }

  static encryptSessionData(sessionData) {
    return DataEncryption.encrypt(sessionData);
  }

  static decryptSessionData(encryptedSessionData) {
    return DataEncryption.decrypt(encryptedSessionData);
  }
}

// Backup code utilities for MFA
class BackupCodeUtils {
  static generateBackupCodes(count = 10) {
    const codes = [];
    for (let i = 0; i < count; i++) {
      // Generate 8-character alphanumeric codes
      const code = crypto.randomBytes(4).toString('hex').toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  static hashBackupCode(code) {
    return crypto.createHash('sha256').update(code.toUpperCase()).digest('hex');
  }

  static verifyBackupCode(code, hash) {
    const codeHash = this.hashBackupCode(code);
    return crypto.timingSafeEqual(
      Buffer.from(codeHash, 'hex'),
      Buffer.from(hash, 'hex')
    );
  }
}

// Key derivation utilities
class KeyDerivation {
  static deriveKey(password, salt, iterations = 100000, keyLength = 32) {
    return crypto.pbkdf2Sync(password, salt, iterations, keyLength, 'sha256');
  }

  static generateSalt(length = 32) {
    return crypto.randomBytes(length);
  }

  static deriveKeyFromString(input, salt = 'smalt-default-salt') {
    return crypto.pbkdf2Sync(input, salt, 10000, 32, 'sha256').toString('hex');
  }
}

// Secure comparison utilities
class SecureComparison {
  static timingSafeEqual(a, b) {
    if (typeof a === 'string') a = Buffer.from(a);
    if (typeof b === 'string') b = Buffer.from(b);
    
    if (a.length !== b.length) {
      return false;
    }
    
    return crypto.timingSafeEqual(a, b);
  }

  static constantTimeStringCompare(a, b) {
    if (a.length !== b.length) {
      return false;
    }
    
    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }
    
    return result === 0;
  }
}

module.exports = {
  PasswordUtils,
  ApiKeyEncryption,
  DataEncryption,
  TokenUtils,
  SessionUtils,
  BackupCodeUtils,
  KeyDerivation,
  SecureComparison
};
