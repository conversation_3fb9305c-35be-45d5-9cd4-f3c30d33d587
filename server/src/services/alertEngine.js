const cron = require('node-cron');
const { query, transaction } = require('../config/database');
const { YahooFinanceAPI, OpenWeatherAPI, NewsAPI } = require('./externalApis');
const NotificationService = require('./notificationService');
const OpenAIService = require('./openaiService');

class AlertEngine {
  constructor() {
    this.isRunning = false;
    this.cronJob = null;
    this.processingAlerts = new Set();
  }

  // Start the alert engine
  start() {
    if (this.isRunning) {
      console.log('Alert engine is already running');
      return;
    }

    const cronExpression = process.env.ALERT_CHECK_INTERVAL || '*/5 * * * *'; // Every 5 minutes
    
    this.cronJob = cron.schedule(cronExpression, async () => {
      await this.processAlerts();
    }, {
      scheduled: false
    });

    this.cronJob.start();
    this.isRunning = true;
    console.log(`Alert engine started with schedule: ${cronExpression}`);
  }

  // Stop the alert engine
  stop() {
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
    }
    this.isRunning = false;
    console.log('Alert engine stopped');
  }

  // Process all active alerts
  async processAlerts() {
    try {
      console.log('Processing alerts...');

      // Get all active alerts that need checking
      const alertsResult = await query(`
        SELECT 
          a.id, a.user_id, a.name, a.description, a.data_source_id,
          a.conditions, a.notification_channels, a.priority,
          a.frequency_minutes, a.max_notifications_per_day,
          a.last_checked, a.last_triggered, a.trigger_count,
          ds.name as data_source_name, ds.type as data_source_type
        FROM alerts a
        JOIN data_sources ds ON a.data_source_id = ds.id
        WHERE a.is_active = true 
          AND ds.is_active = true
          AND (
            a.last_checked IS NULL 
            OR a.last_checked < NOW() - INTERVAL '1 minute' * a.frequency_minutes
          )
        ORDER BY a.priority DESC, a.last_checked ASC NULLS FIRST
      `);

      const alerts = alertsResult.rows;
      console.log(`Found ${alerts.length} alerts to process`);

      // Process alerts in batches to avoid overwhelming external APIs
      const batchSize = 10;
      for (let i = 0; i < alerts.length; i += batchSize) {
        const batch = alerts.slice(i, i + batchSize);
        await Promise.allSettled(
          batch.map(alert => this.processAlert(alert))
        );
        
        // Small delay between batches
        if (i + batchSize < alerts.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      console.log('Alert processing completed');

    } catch (error) {
      console.error('Error processing alerts:', error);
    }
  }

  // Process a single alert
  async processAlert(alert) {
    const alertId = alert.id;

    // Prevent duplicate processing
    if (this.processingAlerts.has(alertId)) {
      return;
    }

    this.processingAlerts.add(alertId);

    try {
      console.log(`Processing alert: ${alert.name} (${alertId})`);

      // Update last_checked timestamp
      await query(
        'UPDATE alerts SET last_checked = CURRENT_TIMESTAMP WHERE id = $1',
        [alertId]
      );

      // Check daily notification limit
      const notificationCount = await this.getNotificationCount(alert.user_id, alertId);
      if (notificationCount >= alert.max_notifications_per_day) {
        console.log(`Alert ${alertId} has reached daily notification limit`);
        return;
      }

      // Fetch data based on data source type
      let data;
      try {
        data = await this.fetchAlertData(alert);
      } catch (fetchError) {
        console.error(`Failed to fetch data for alert ${alertId}:`, fetchError);
        return;
      }

      // Evaluate alert conditions
      const conditionsMet = this.evaluateConditions(alert.conditions, data);

      if (conditionsMet.triggered) {
        console.log(`Alert ${alertId} conditions met, triggering notification`);
        
        // Update alert trigger information
        await query(`
          UPDATE alerts 
          SET last_triggered = CURRENT_TIMESTAMP, trigger_count = trigger_count + 1
          WHERE id = $1
        `, [alertId]);

        // Send notifications
        await this.sendNotifications(alert, data, conditionsMet);

        // Generate AI insights if enabled
        if (alert.ai_optimized) {
          try {
            await this.generateAIInsights(alert, data, conditionsMet);
          } catch (aiError) {
            console.error(`AI insights failed for alert ${alertId}:`, aiError);
          }
        }
      }

    } catch (error) {
      console.error(`Error processing alert ${alertId}:`, error);
    } finally {
      this.processingAlerts.delete(alertId);
    }
  }

  // Fetch data for an alert based on its data source
  async fetchAlertData(alert) {
    const { data_source_type, conditions } = alert;

    switch (data_source_type) {
      case 'stock':
        return await this.fetchStockData(alert.user_id, conditions);
      
      case 'weather':
        return await this.fetchWeatherData(alert.user_id, conditions);
      
      case 'news':
        return await this.fetchNewsData(alert.user_id, conditions);
      
      default:
        throw new Error(`Unsupported data source type: ${data_source_type}`);
    }
  }

  // Fetch stock data
  async fetchStockData(userId, conditions) {
    const symbol = conditions.symbol;
    if (!symbol) {
      throw new Error('Stock symbol not specified in conditions');
    }

    return await YahooFinanceAPI.getQuote(userId, symbol);
  }

  // Fetch weather data
  async fetchWeatherData(userId, conditions) {
    const location = conditions.location;
    if (!location) {
      throw new Error('Location not specified in conditions');
    }

    return await OpenWeatherAPI.getCurrentWeather(userId, location);
  }

  // Fetch news data
  async fetchNewsData(userId, conditions) {
    const query = conditions.query || conditions.keywords;
    if (!query) {
      throw new Error('Search query not specified in conditions');
    }

    const options = {
      sortBy: conditions.sortBy || 'publishedAt',
      language: conditions.language || 'en',
      pageSize: 10
    };

    return await NewsAPI.searchNews(userId, query, options);
  }

  // Evaluate alert conditions against fetched data
  evaluateConditions(conditions, data) {
    const results = {
      triggered: false,
      matchedConditions: [],
      failedConditions: [],
      data: data
    };

    try {
      // Handle different condition formats
      if (conditions.rules && Array.isArray(conditions.rules)) {
        // Complex rule-based conditions
        results.triggered = this.evaluateRules(conditions.rules, data, results);
      } else {
        // Simple field-based conditions
        results.triggered = this.evaluateSimpleConditions(conditions, data, results);
      }

    } catch (error) {
      console.error('Error evaluating conditions:', error);
      results.triggered = false;
      results.error = error.message;
    }

    return results;
  }

  // Evaluate rule-based conditions
  evaluateRules(rules, data, results) {
    let triggered = false;

    for (const rule of rules) {
      const ruleResult = this.evaluateRule(rule, data);
      
      if (ruleResult.matched) {
        results.matchedConditions.push(ruleResult);
        
        // Handle logical operators
        if (rule.logicalOperator === 'OR' || !triggered) {
          triggered = true;
        }
      } else {
        results.failedConditions.push(ruleResult);
        
        // For AND operations, one failure means overall failure
        if (rule.logicalOperator === 'AND') {
          triggered = false;
          break;
        }
      }
    }

    return triggered;
  }

  // Evaluate a single rule
  evaluateRule(rule, data) {
    const { field, operator, value } = rule;
    const dataValue = this.getNestedValue(data, field);

    const result = {
      field,
      operator,
      expectedValue: value,
      actualValue: dataValue,
      matched: false
    };

    if (dataValue === undefined || dataValue === null) {
      return result;
    }

    switch (operator) {
      case 'gt':
      case '>':
        result.matched = parseFloat(dataValue) > parseFloat(value);
        break;
      
      case 'lt':
      case '<':
        result.matched = parseFloat(dataValue) < parseFloat(value);
        break;
      
      case 'gte':
      case '>=':
        result.matched = parseFloat(dataValue) >= parseFloat(value);
        break;
      
      case 'lte':
      case '<=':
        result.matched = parseFloat(dataValue) <= parseFloat(value);
        break;
      
      case 'eq':
      case '==':
        result.matched = dataValue.toString() === value.toString();
        break;
      
      case 'ne':
      case '!=':
        result.matched = dataValue.toString() !== value.toString();
        break;
      
      case 'contains':
        result.matched = dataValue.toString().toLowerCase().includes(value.toLowerCase());
        break;
      
      case 'not_contains':
        result.matched = !dataValue.toString().toLowerCase().includes(value.toLowerCase());
        break;
      
      case 'starts_with':
        result.matched = dataValue.toString().toLowerCase().startsWith(value.toLowerCase());
        break;
      
      case 'ends_with':
        result.matched = dataValue.toString().toLowerCase().endsWith(value.toLowerCase());
        break;
      
      case 'regex':
        try {
          const regex = new RegExp(value, 'i');
          result.matched = regex.test(dataValue.toString());
        } catch (regexError) {
          result.matched = false;
          result.error = 'Invalid regex pattern';
        }
        break;
      
      default:
        result.matched = false;
        result.error = `Unknown operator: ${operator}`;
    }

    return result;
  }

  // Evaluate simple field-based conditions
  evaluateSimpleConditions(conditions, data, results) {
    // This is a simplified version for basic conditions
    // Implementation depends on the specific condition format
    return false;
  }

  // Get nested value from object using dot notation
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  // Get notification count for today
  async getNotificationCount(userId, alertId) {
    const result = await query(`
      SELECT COUNT(*) as count
      FROM notifications
      WHERE user_id = $1 
        AND alert_id = $2 
        AND created_at >= CURRENT_DATE
    `, [userId, alertId]);

    return parseInt(result.rows[0].count);
  }

  // Send notifications for triggered alert
  async sendNotifications(alert, data, conditionResults) {
    try {
      const notificationChannels = alert.notification_channels;
      
      for (const channel of notificationChannels) {
        await NotificationService.sendNotification({
          userId: alert.user_id,
          alertId: alert.id,
          type: channel,
          alert: alert,
          data: data,
          conditionResults: conditionResults
        });
      }

    } catch (error) {
      console.error(`Failed to send notifications for alert ${alert.id}:`, error);
    }
  }

  // Generate AI insights for the alert
  async generateAIInsights(alert, data, conditionResults) {
    try {
      const insights = await OpenAIService.analyzeDataPatterns(
        alert.user_id,
        [data],
        alert.data_source_type
      );

      // Store insights in the alert
      await query(`
        UPDATE alerts 
        SET ai_suggestions = $1
        WHERE id = $2
      `, [JSON.stringify(insights), alert.id]);

    } catch (error) {
      console.error(`Failed to generate AI insights for alert ${alert.id}:`, error);
    }
  }

  // Manual alert check (for testing or immediate execution)
  async checkAlert(alertId) {
    try {
      const alertResult = await query(`
        SELECT 
          a.id, a.user_id, a.name, a.description, a.data_source_id,
          a.conditions, a.notification_channels, a.priority,
          a.frequency_minutes, a.max_notifications_per_day,
          a.last_checked, a.last_triggered, a.trigger_count,
          ds.name as data_source_name, ds.type as data_source_type
        FROM alerts a
        JOIN data_sources ds ON a.data_source_id = ds.id
        WHERE a.id = $1 AND a.is_active = true AND ds.is_active = true
      `, [alertId]);

      if (alertResult.rows.length === 0) {
        throw new Error('Alert not found or inactive');
      }

      const alert = alertResult.rows[0];
      await this.processAlert(alert);
      
      return { success: true, message: 'Alert checked successfully' };

    } catch (error) {
      console.error(`Manual alert check failed for ${alertId}:`, error);
      throw error;
    }
  }
}

// Create singleton instance
const alertEngine = new AlertEngine();

module.exports = alertEngine;
