const OpenAI = require('openai');
const CryptoJS = require('crypto-js');
const { query } = require('../config/database');

// Decrypt API key
const decryptApiKey = (encryptedApiKey) => {
  const bytes = CryptoJS.AES.decrypt(encryptedApiKey, process.env.ENCRYPTION_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};

// Get user's OpenAI configuration
const getUserOpenAIConfig = async (userId) => {
  const result = await query(`
    SELECT api_key_encrypted, api_endpoint, rate_limit_per_hour
    FROM user_api_configs
    WHERE user_id = $1 AND service_name = 'openai' AND is_active = true
  `, [userId]);

  if (result.rows.length === 0) {
    throw new Error('OpenAI API configuration not found');
  }

  const config = result.rows[0];
  return {
    apiKey: decryptApiKey(config.api_key_encrypted),
    endpoint: config.api_endpoint,
    rateLimit: config.rate_limit_per_hour
  };
};

// OpenAI Service Class
class OpenAIService {
  static async getClient(userId) {
    const config = await getUserOpenAIConfig(userId);
    
    const clientConfig = {
      apiKey: config.apiKey
    };

    if (config.endpoint && config.endpoint !== 'https://api.openai.com/v1') {
      clientConfig.baseURL = config.endpoint;
    }

    return new OpenAI(clientConfig);
  }

  // Generate alert optimization suggestions
  static async optimizeAlert(userId, alertData) {
    try {
      const client = await this.getClient(userId);

      const prompt = `
        Analyze this alert configuration and provide optimization suggestions:
        
        Alert Name: ${alertData.name}
        Description: ${alertData.description || 'No description'}
        Data Source: ${alertData.dataSource}
        Conditions: ${JSON.stringify(alertData.conditions)}
        Frequency: ${alertData.frequencyMinutes} minutes
        Priority: ${alertData.priority}
        
        Please provide:
        1. Suggestions to improve the alert conditions
        2. Recommended frequency based on the alert type
        3. Priority level recommendations
        4. Potential false positive reduction strategies
        
        Respond in JSON format with the following structure:
        {
          "suggestions": [
            {
              "type": "condition|frequency|priority|general",
              "title": "Brief title",
              "description": "Detailed suggestion",
              "impact": "high|medium|low"
            }
          ],
          "optimizedConditions": {
            // Suggested improved conditions
          },
          "recommendedFrequency": number,
          "recommendedPriority": "low|medium|high|critical"
        }
      `;

      const response = await client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert in alert system optimization. Provide practical, actionable suggestions to improve alert effectiveness and reduce noise.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.3
      });

      const content = response.choices[0].message.content;
      
      try {
        return JSON.parse(content);
      } catch (parseError) {
        // If JSON parsing fails, return a structured response
        return {
          suggestions: [
            {
              type: 'general',
              title: 'AI Analysis Available',
              description: content,
              impact: 'medium'
            }
          ],
          optimizedConditions: alertData.conditions,
          recommendedFrequency: alertData.frequencyMinutes,
          recommendedPriority: alertData.priority
        };
      }

    } catch (error) {
      console.error('OpenAI alert optimization error:', error);
      throw new Error(`Failed to optimize alert: ${error.message}`);
    }
  }

  // Generate smart alert recommendations based on user behavior
  static async generateAlertRecommendations(userId, userAlerts, userPreferences) {
    try {
      const client = await this.getClient(userId);

      const prompt = `
        Based on this user's existing alerts and preferences, suggest new alert ideas:
        
        Existing Alerts:
        ${userAlerts.map(alert => `- ${alert.name} (${alert.dataSource}, ${alert.priority})`).join('\n')}
        
        User Preferences:
        - Notification channels: ${userPreferences.notificationChannels?.join(', ') || 'Not specified'}
        - Preferred frequency: ${userPreferences.frequency || 'Not specified'}
        - Industries of interest: ${userPreferences.industries?.join(', ') || 'Not specified'}
        
        Suggest 3-5 new alert ideas that would complement their existing setup.
        
        Respond in JSON format:
        {
          "recommendations": [
            {
              "name": "Alert name",
              "description": "What this alert does",
              "dataSource": "stock|weather|news",
              "category": "category name",
              "priority": "low|medium|high|critical",
              "reasoning": "Why this alert would be valuable",
              "sampleConditions": {
                // Example conditions for this alert
              }
            }
          ]
        }
      `;

      const response = await client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert in alert systems and financial/news monitoring. Provide personalized, practical alert recommendations.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1200,
        temperature: 0.4
      });

      const content = response.choices[0].message.content;
      
      try {
        return JSON.parse(content);
      } catch (parseError) {
        return {
          recommendations: [
            {
              name: 'AI-Generated Recommendation',
              description: content,
              dataSource: 'custom',
              category: 'AI Insights',
              priority: 'medium',
              reasoning: 'Generated by AI analysis',
              sampleConditions: {}
            }
          ]
        };
      }

    } catch (error) {
      console.error('OpenAI recommendations error:', error);
      throw new Error(`Failed to generate recommendations: ${error.message}`);
    }
  }

  // Enhance notification content with AI
  static async enhanceNotificationContent(userId, alertData, triggerData) {
    try {
      const client = await this.getClient(userId);

      const prompt = `
        Enhance this alert notification with context and insights:
        
        Alert: ${alertData.name}
        Trigger Data: ${JSON.stringify(triggerData)}
        Data Source: ${alertData.dataSource}
        
        Create an enhanced notification that includes:
        1. Clear summary of what happened
        2. Context about why this is significant
        3. Potential implications or next steps
        4. Keep it concise but informative
        
        Respond in JSON format:
        {
          "subject": "Enhanced email subject",
          "summary": "Brief summary of the trigger",
          "context": "Why this is significant",
          "implications": "What this might mean",
          "actionItems": ["Suggested actions"],
          "enhancedContent": "Full enhanced notification text"
        }
      `;

      const response = await client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert analyst who helps users understand the significance of their alerts and data changes.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 800,
        temperature: 0.3
      });

      const content = response.choices[0].message.content;
      
      try {
        return JSON.parse(content);
      } catch (parseError) {
        return {
          subject: `Alert: ${alertData.name}`,
          summary: 'Alert triggered',
          context: content,
          implications: 'Review the data for potential actions',
          actionItems: ['Review alert conditions', 'Check data source'],
          enhancedContent: content
        };
      }

    } catch (error) {
      console.error('OpenAI content enhancement error:', error);
      throw new Error(`Failed to enhance content: ${error.message}`);
    }
  }

  // Analyze data patterns and suggest insights
  static async analyzeDataPatterns(userId, dataPoints, dataType) {
    try {
      const client = await this.getClient(userId);

      const prompt = `
        Analyze these data points and provide insights:
        
        Data Type: ${dataType}
        Data Points: ${JSON.stringify(dataPoints.slice(-20))} // Last 20 points
        
        Provide analysis including:
        1. Trends and patterns
        2. Anomalies or unusual behavior
        3. Potential future movements
        4. Risk factors to watch
        
        Respond in JSON format:
        {
          "trends": ["Identified trends"],
          "anomalies": ["Unusual patterns"],
          "insights": ["Key insights"],
          "recommendations": ["Actionable recommendations"],
          "riskFactors": ["Things to monitor"],
          "confidence": "high|medium|low"
        }
      `;

      const response = await client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a data analyst expert who identifies patterns, trends, and provides actionable insights from data.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.3
      });

      const content = response.choices[0].message.content;
      
      try {
        return JSON.parse(content);
      } catch (parseError) {
        return {
          trends: ['Analysis available'],
          anomalies: [],
          insights: [content],
          recommendations: ['Review the analysis'],
          riskFactors: [],
          confidence: 'medium'
        };
      }

    } catch (error) {
      console.error('OpenAI pattern analysis error:', error);
      throw new Error(`Failed to analyze patterns: ${error.message}`);
    }
  }
}

module.exports = OpenAIService;
