const axios = require('axios');
const CryptoJS = require('crypto-js');
const { query } = require('../config/database');

// Rate limiting cache
const rateLimitCache = new Map();

// Decrypt API key
const decryptApiKey = (encryptedApiKey) => {
  const bytes = CryptoJS.AES.decrypt(encryptedApiKey, process.env.ENCRYPTION_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};

// Check rate limit
const checkRateLimit = (userId, serviceName, limit) => {
  const key = `${userId}:${serviceName}`;
  const now = Date.now();
  const hourAgo = now - 60 * 60 * 1000;

  if (!rateLimitCache.has(key)) {
    rateLimitCache.set(key, []);
  }

  const requests = rateLimitCache.get(key).filter(time => time > hourAgo);
  
  if (requests.length >= limit) {
    return false;
  }

  requests.push(now);
  rateLimitCache.set(key, requests);
  return true;
};

// Get user API configuration
const getUserApiConfig = async (userId, serviceName) => {
  const result = await query(`
    SELECT api_key_encrypted, api_endpoint, rate_limit_per_hour
    FROM user_api_configs
    WHERE user_id = $1 AND service_name = $2 AND is_active = true
  `, [userId, serviceName]);

  if (result.rows.length === 0) {
    throw new Error(`API configuration for ${serviceName} not found`);
  }

  const config = result.rows[0];
  return {
    apiKey: decryptApiKey(config.api_key_encrypted),
    endpoint: config.api_endpoint,
    rateLimit: config.rate_limit_per_hour
  };
};

// Yahoo Finance API Integration
class YahooFinanceAPI {
  static async getQuote(userId, symbol) {
    try {
      const config = await getUserApiConfig(userId, 'yahoo_finance');
      
      if (!checkRateLimit(userId, 'yahoo_finance', config.rateLimit)) {
        throw new Error('Rate limit exceeded for Yahoo Finance API');
      }

      const url = `https://query1.finance.yahoo.com/v8/finance/chart/${symbol}`;
      
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'SMALT Alert System'
        }
      });

      if (!response.data.chart.result || response.data.chart.result.length === 0) {
        throw new Error('No data found for symbol');
      }

      const result = response.data.chart.result[0];
      const meta = result.meta;
      const quote = result.indicators.quote[0];

      return {
        symbol: meta.symbol,
        price: meta.regularMarketPrice,
        previousClose: meta.previousClose,
        change: meta.regularMarketPrice - meta.previousClose,
        changePercent: ((meta.regularMarketPrice - meta.previousClose) / meta.previousClose) * 100,
        volume: quote.volume[quote.volume.length - 1],
        marketCap: meta.marketCap,
        currency: meta.currency,
        timestamp: new Date(meta.regularMarketTime * 1000)
      };

    } catch (error) {
      console.error('Yahoo Finance API error:', error);
      throw new Error(`Failed to fetch stock data: ${error.message}`);
    }
  }

  static async searchSymbols(userId, query) {
    try {
      const config = await getUserApiConfig(userId, 'yahoo_finance');
      
      if (!checkRateLimit(userId, 'yahoo_finance', config.rateLimit)) {
        throw new Error('Rate limit exceeded for Yahoo Finance API');
      }

      const url = `https://query1.finance.yahoo.com/v1/finance/search`;
      
      const response = await axios.get(url, {
        params: { q: query },
        timeout: 10000,
        headers: {
          'User-Agent': 'SMALT Alert System'
        }
      });

      return response.data.quotes.map(quote => ({
        symbol: quote.symbol,
        name: quote.shortname || quote.longname,
        type: quote.quoteType,
        exchange: quote.exchange
      }));

    } catch (error) {
      console.error('Yahoo Finance search error:', error);
      throw new Error(`Failed to search symbols: ${error.message}`);
    }
  }
}

// OpenWeatherMap API Integration
class OpenWeatherAPI {
  static async getCurrentWeather(userId, location) {
    try {
      const config = await getUserApiConfig(userId, 'openweather');
      
      if (!checkRateLimit(userId, 'openweather', config.rateLimit)) {
        throw new Error('Rate limit exceeded for OpenWeather API');
      }

      const url = 'https://api.openweathermap.org/data/2.5/weather';
      
      const response = await axios.get(url, {
        params: {
          q: location,
          appid: config.apiKey,
          units: 'metric'
        },
        timeout: 10000
      });

      const data = response.data;

      return {
        location: `${data.name}, ${data.sys.country}`,
        temperature: data.main.temp,
        feelsLike: data.main.feels_like,
        humidity: data.main.humidity,
        pressure: data.main.pressure,
        windSpeed: data.wind.speed,
        windDirection: data.wind.deg,
        visibility: data.visibility,
        condition: data.weather[0].main.toLowerCase(),
        description: data.weather[0].description,
        icon: data.weather[0].icon,
        timestamp: new Date(data.dt * 1000)
      };

    } catch (error) {
      console.error('OpenWeather API error:', error);
      if (error.response?.status === 404) {
        throw new Error('Location not found');
      }
      throw new Error(`Failed to fetch weather data: ${error.message}`);
    }
  }

  static async getForecast(userId, location, days = 5) {
    try {
      const config = await getUserApiConfig(userId, 'openweather');
      
      if (!checkRateLimit(userId, 'openweather', config.rateLimit)) {
        throw new Error('Rate limit exceeded for OpenWeather API');
      }

      const url = 'https://api.openweathermap.org/data/2.5/forecast';
      
      const response = await axios.get(url, {
        params: {
          q: location,
          appid: config.apiKey,
          units: 'metric',
          cnt: days * 8 // 8 forecasts per day (3-hour intervals)
        },
        timeout: 10000
      });

      const data = response.data;

      return {
        location: `${data.city.name}, ${data.city.country}`,
        forecasts: data.list.map(item => ({
          timestamp: new Date(item.dt * 1000),
          temperature: item.main.temp,
          feelsLike: item.main.feels_like,
          humidity: item.main.humidity,
          pressure: item.main.pressure,
          windSpeed: item.wind.speed,
          condition: item.weather[0].main.toLowerCase(),
          description: item.weather[0].description,
          icon: item.weather[0].icon
        }))
      };

    } catch (error) {
      console.error('OpenWeather forecast error:', error);
      throw new Error(`Failed to fetch weather forecast: ${error.message}`);
    }
  }
}

// NewsAPI Integration
class NewsAPI {
  static async searchNews(userId, query, options = {}) {
    try {
      const config = await getUserApiConfig(userId, 'newsapi');
      
      if (!checkRateLimit(userId, 'newsapi', config.rateLimit)) {
        throw new Error('Rate limit exceeded for News API');
      }

      const url = 'https://newsapi.org/v2/everything';
      
      const params = {
        q: query,
        apiKey: config.apiKey,
        sortBy: options.sortBy || 'publishedAt',
        language: options.language || 'en',
        pageSize: options.pageSize || 20,
        page: options.page || 1
      };

      if (options.from) params.from = options.from;
      if (options.to) params.to = options.to;
      if (options.sources) params.sources = options.sources;

      const response = await axios.get(url, {
        params,
        timeout: 10000
      });

      const data = response.data;

      return {
        totalResults: data.totalResults,
        articles: data.articles.map(article => ({
          title: article.title,
          description: article.description,
          content: article.content,
          url: article.url,
          urlToImage: article.urlToImage,
          publishedAt: new Date(article.publishedAt),
          source: {
            id: article.source.id,
            name: article.source.name
          },
          author: article.author
        }))
      };

    } catch (error) {
      console.error('NewsAPI error:', error);
      if (error.response?.status === 429) {
        throw new Error('News API rate limit exceeded');
      }
      throw new Error(`Failed to fetch news: ${error.message}`);
    }
  }

  static async getTopHeadlines(userId, options = {}) {
    try {
      const config = await getUserApiConfig(userId, 'newsapi');
      
      if (!checkRateLimit(userId, 'newsapi', config.rateLimit)) {
        throw new Error('Rate limit exceeded for News API');
      }

      const url = 'https://newsapi.org/v2/top-headlines';
      
      const params = {
        apiKey: config.apiKey,
        pageSize: options.pageSize || 20,
        page: options.page || 1
      };

      if (options.country) params.country = options.country;
      if (options.category) params.category = options.category;
      if (options.sources) params.sources = options.sources;

      const response = await axios.get(url, {
        params,
        timeout: 10000
      });

      const data = response.data;

      return {
        totalResults: data.totalResults,
        articles: data.articles.map(article => ({
          title: article.title,
          description: article.description,
          content: article.content,
          url: article.url,
          urlToImage: article.urlToImage,
          publishedAt: new Date(article.publishedAt),
          source: {
            id: article.source.id,
            name: article.source.name
          },
          author: article.author
        }))
      };

    } catch (error) {
      console.error('NewsAPI headlines error:', error);
      throw new Error(`Failed to fetch headlines: ${error.message}`);
    }
  }
}

module.exports = {
  YahooFinanceAPI,
  OpenWeatherAPI,
  NewsAPI,
  checkRateLimit,
  getUserApiConfig
};
