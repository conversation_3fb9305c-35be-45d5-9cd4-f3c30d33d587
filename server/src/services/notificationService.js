const { Resend } = require('resend');
const webpush = require('web-push');
const { query, transaction } = require('../config/database');
const OpenAIService = require('./openaiService');

// Initialize services
const resend = new Resend(process.env.RESEND_API_KEY);

// Configure web push
webpush.setVapidDetails(
  process.env.VAPID_SUBJECT || 'mailto:<EMAIL>',
  process.env.VAPID_PUBLIC_KEY,
  process.env.VAPID_PRIVATE_KEY
);

class NotificationService {
  // Main notification sending method
  static async sendNotification(notificationData) {
    const {
      userId,
      alertId,
      type,
      alert,
      data,
      conditionResults,
      customContent
    } = notificationData;

    try {
      // Create notification record
      const notificationId = await this.createNotificationRecord({
        userId,
        alertId,
        type,
        alert,
        data,
        conditionResults,
        customContent
      });

      // Send based on type
      switch (type) {
        case 'email':
          await this.sendEmailNotification(notificationId, notificationData);
          break;
        
        case 'push':
          await this.sendPushNotification(notificationId, notificationData);
          break;
        
        case 'sms':
          await this.sendSMSNotification(notificationId, notificationData);
          break;
        
        default:
          throw new Error(`Unsupported notification type: ${type}`);
      }

      return { success: true, notificationId };

    } catch (error) {
      console.error('Notification sending failed:', error);
      
      // Update notification record with error
      if (notificationData.notificationId) {
        await this.updateNotificationStatus(
          notificationData.notificationId,
          'failed',
          error.message
        );
      }

      throw error;
    }
  }

  // Create notification record in database
  static async createNotificationRecord(notificationData) {
    const {
      userId,
      alertId,
      type,
      alert,
      data,
      conditionResults,
      customContent
    } = notificationData;

    // Generate notification content
    const content = customContent || await this.generateNotificationContent(
      alert,
      data,
      conditionResults
    );

    // Determine channel based on type
    let channel;
    switch (type) {
      case 'email':
        const userResult = await query('SELECT email FROM users WHERE id = $1', [userId]);
        channel = userResult.rows[0]?.email;
        break;
      case 'push':
        channel = 'browser_push';
        break;
      case 'sms':
        // TODO: Get user's phone number
        channel = 'sms_pending';
        break;
      default:
        channel = type;
    }

    const result = await query(`
      INSERT INTO notifications (
        user_id, alert_id, type, channel, subject, content, metadata
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id
    `, [
      userId,
      alertId,
      type,
      channel,
      content.subject,
      content.body,
      JSON.stringify({
        data: data,
        conditionResults: conditionResults,
        timestamp: new Date().toISOString()
      })
    ]);

    return result.rows[0].id;
  }

  // Generate notification content
  static async generateNotificationContent(alert, data, conditionResults) {
    const baseContent = {
      subject: `Alert: ${alert.name}`,
      body: this.generateBasicContent(alert, data, conditionResults)
    };

    // Try to enhance with AI if available
    try {
      if (alert.ai_optimized) {
        const enhanced = await OpenAIService.enhanceNotificationContent(
          alert.user_id,
          alert,
          { data, conditionResults }
        );
        
        return {
          subject: enhanced.subject || baseContent.subject,
          body: enhanced.enhancedContent || baseContent.body,
          enhanced: true
        };
      }
    } catch (aiError) {
      console.error('AI content enhancement failed:', aiError);
    }

    return baseContent;
  }

  // Generate basic notification content
  static generateBasicContent(alert, data, conditionResults) {
    let content = `Your alert "${alert.name}" has been triggered.\n\n`;

    // Add condition details
    if (conditionResults.matchedConditions?.length > 0) {
      content += 'Conditions met:\n';
      conditionResults.matchedConditions.forEach(condition => {
        content += `- ${condition.field} ${condition.operator} ${condition.expectedValue} (actual: ${condition.actualValue})\n`;
      });
      content += '\n';
    }

    // Add data summary based on type
    if (alert.data_source_type === 'stock' && data.symbol) {
      content += `Stock: ${data.symbol}\n`;
      content += `Price: ${data.currency} ${data.price}\n`;
      content += `Change: ${data.change > 0 ? '+' : ''}${data.change.toFixed(2)} (${data.changePercent.toFixed(2)}%)\n`;
      content += `Volume: ${data.volume?.toLocaleString() || 'N/A'}\n`;
    } else if (alert.data_source_type === 'weather' && data.location) {
      content += `Location: ${data.location}\n`;
      content += `Temperature: ${data.temperature}°C (feels like ${data.feelsLike}°C)\n`;
      content += `Condition: ${data.description}\n`;
      content += `Humidity: ${data.humidity}%\n`;
      content += `Wind: ${data.windSpeed} m/s\n`;
    } else if (alert.data_source_type === 'news' && data.articles) {
      content += `Found ${data.totalResults} articles\n`;
      if (data.articles.length > 0) {
        content += `Latest: ${data.articles[0].title}\n`;
        content += `Source: ${data.articles[0].source.name}\n`;
      }
    }

    content += `\nTriggered at: ${new Date().toLocaleString()}\n`;
    content += `Priority: ${alert.priority}\n`;

    return content;
  }

  // Send email notification
  static async sendEmailNotification(notificationId, notificationData) {
    try {
      const { userId, alert } = notificationData;

      // Get user email
      const userResult = await query('SELECT email, first_name FROM users WHERE id = $1', [userId]);
      if (userResult.rows.length === 0) {
        throw new Error('User not found');
      }

      const user = userResult.rows[0];

      // Get notification content
      const notificationResult = await query(
        'SELECT subject, content FROM notifications WHERE id = $1',
        [notificationId]
      );

      if (notificationResult.rows.length === 0) {
        throw new Error('Notification record not found');
      }

      const notification = notificationResult.rows[0];

      // Send email via Resend
      const emailData = {
        from: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
        to: [user.email],
        subject: notification.subject,
        text: notification.content,
        html: this.generateEmailHTML(notification.content, alert, user.first_name)
      };

      const result = await resend.emails.send(emailData);

      // Update notification status
      await this.updateNotificationStatus(notificationId, 'sent', null, {
        resendId: result.id
      });

      console.log(`Email sent successfully to ${user.email} (Resend ID: ${result.id})`);

    } catch (error) {
      console.error('Email sending failed:', error);
      await this.updateNotificationStatus(notificationId, 'failed', error.message);
      throw error;
    }
  }

  // Generate HTML email content
  static generateEmailHTML(textContent, alert, firstName) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>SMALT Alert</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2563eb; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f8fafc; padding: 20px; border-radius: 0 0 8px 8px; }
          .alert-info { background: white; padding: 15px; border-radius: 6px; margin: 15px 0; }
          .priority-high { border-left: 4px solid #ef4444; }
          .priority-medium { border-left: 4px solid #f59e0b; }
          .priority-low { border-left: 4px solid #10b981; }
          .priority-critical { border-left: 4px solid #dc2626; }
          .footer { text-align: center; margin-top: 20px; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚨 SMALT Alert System</h1>
          </div>
          <div class="content">
            <h2>Hello ${firstName},</h2>
            <div class="alert-info priority-${alert.priority}">
              <h3>${alert.name}</h3>
              <pre style="white-space: pre-wrap; font-family: inherit;">${textContent}</pre>
            </div>
            <div class="footer">
              <p>This alert was generated by the SMALT Alert System.</p>
              <p>To manage your alerts, visit your dashboard.</p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Send push notification
  static async sendPushNotification(notificationId, notificationData) {
    try {
      const { userId, alert } = notificationData;

      // Get user's push subscriptions
      const subscriptionsResult = await query(`
        SELECT endpoint, p256dh_key, auth_key
        FROM push_subscriptions
        WHERE user_id = $1 AND is_active = true
      `, [userId]);

      if (subscriptionsResult.rows.length === 0) {
        throw new Error('No active push subscriptions found');
      }

      // Get notification content
      const notificationResult = await query(
        'SELECT subject, content FROM notifications WHERE id = $1',
        [notificationId]
      );

      const notification = notificationResult.rows[0];

      // Prepare push payload
      const payload = JSON.stringify({
        title: notification.subject,
        body: notification.content.substring(0, 200) + (notification.content.length > 200 ? '...' : ''),
        icon: '/icon-192x192.png',
        badge: '/badge-72x72.png',
        tag: `alert-${alert.id}`,
        data: {
          alertId: alert.id,
          alertName: alert.name,
          priority: alert.priority,
          timestamp: new Date().toISOString()
        },
        actions: [
          {
            action: 'view',
            title: 'View Alert'
          },
          {
            action: 'dismiss',
            title: 'Dismiss'
          }
        ]
      });

      // Send to all subscriptions
      const sendPromises = subscriptionsResult.rows.map(async (subscription) => {
        try {
          const pushSubscription = {
            endpoint: subscription.endpoint,
            keys: {
              p256dh: subscription.p256dh_key,
              auth: subscription.auth_key
            }
          };

          await webpush.sendNotification(pushSubscription, payload);
          return { success: true, endpoint: subscription.endpoint };
        } catch (error) {
          console.error(`Push notification failed for endpoint ${subscription.endpoint}:`, error);
          
          // If subscription is invalid, deactivate it
          if (error.statusCode === 410) {
            await query(
              'UPDATE push_subscriptions SET is_active = false WHERE endpoint = $1',
              [subscription.endpoint]
            );
          }
          
          return { success: false, endpoint: subscription.endpoint, error: error.message };
        }
      });

      const results = await Promise.allSettled(sendPromises);
      const successCount = results.filter(r => r.value?.success).length;

      // Update notification status
      await this.updateNotificationStatus(notificationId, 'sent', null, {
        totalSubscriptions: subscriptionsResult.rows.length,
        successfulSends: successCount,
        results: results.map(r => r.value)
      });

      console.log(`Push notifications sent: ${successCount}/${subscriptionsResult.rows.length}`);

    } catch (error) {
      console.error('Push notification failed:', error);
      await this.updateNotificationStatus(notificationId, 'failed', error.message);
      throw error;
    }
  }

  // Send SMS notification (placeholder)
  static async sendSMSNotification(notificationId, notificationData) {
    // TODO: Implement SMS sending (Twilio, AWS SNS, etc.)
    console.log('SMS notifications not implemented yet');
    await this.updateNotificationStatus(notificationId, 'failed', 'SMS not implemented');
  }

  // Update notification status
  static async updateNotificationStatus(notificationId, status, errorMessage = null, metadata = null) {
    const updateFields = ['status = $2'];
    const values = [notificationId, status];
    let paramCount = 3;

    if (status === 'sent') {
      updateFields.push(`sent_at = CURRENT_TIMESTAMP`);
    }

    if (errorMessage) {
      updateFields.push(`error_message = $${paramCount++}`);
      values.push(errorMessage);
    }

    if (metadata) {
      updateFields.push(`metadata = metadata || $${paramCount++}`);
      values.push(JSON.stringify(metadata));
    }

    await query(`
      UPDATE notifications 
      SET ${updateFields.join(', ')}
      WHERE id = $1
    `, values);
  }

  // Retry failed notifications
  static async retryFailedNotifications() {
    try {
      const maxRetries = parseInt(process.env.NOTIFICATION_RETRY_ATTEMPTS) || 3;

      const failedNotifications = await query(`
        SELECT id, user_id, alert_id, type, retry_count
        FROM notifications
        WHERE status = 'failed' 
          AND retry_count < $1
          AND created_at > NOW() - INTERVAL '24 hours'
        ORDER BY created_at DESC
        LIMIT 50
      `, [maxRetries]);

      for (const notification of failedNotifications.rows) {
        try {
          // Increment retry count
          await query(
            'UPDATE notifications SET retry_count = retry_count + 1 WHERE id = $1',
            [notification.id]
          );

          // TODO: Retry the notification
          console.log(`Retrying notification ${notification.id} (attempt ${notification.retry_count + 1})`);

        } catch (retryError) {
          console.error(`Retry failed for notification ${notification.id}:`, retryError);
        }
      }

    } catch (error) {
      console.error('Failed to retry notifications:', error);
    }
  }
}

module.exports = NotificationService;
