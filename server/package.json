{"name": "smalt-alert-server", "version": "1.0.0", "description": "Backend API for SMALT Alert System", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "node src/scripts/migrate.js", "db:seed": "node src/scripts/seed.js", "db:reset": "node src/scripts/reset.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "pg": "^8.11.3", "pg-pool": "^3.6.1", "dotenv": "^16.3.1", "axios": "^1.6.2", "node-cron": "^3.0.3", "web-push": "^3.6.6", "resend": "^2.1.0", "openai": "^4.20.1", "crypto-js": "^4.2.0", "joi": "^17.11.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.11.0", "express-winston": "^4.2.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/index.js", "!src/scripts/**"]}}