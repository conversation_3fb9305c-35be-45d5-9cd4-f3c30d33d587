---
sidebar_position: 8
---

# Contributing

- Fork the project and clone it locally.
- Create branches for each separate topic. Any standard you are used to follow for [semantic commit messages](https://gist.github.com/joshbuchea/6f47e86d2510bce28f8e7f42ae84c716) will be highly appreciated.
- Comment your code as if you are going to maintain it in the future.
- Use the rich set of unit tests as an example and add your unit tests as well. This will not only enable you to programatically reproduce your fix faster than setting up an application, but it will also make you super cool! :)
- Push to your changes to the origin of your repository and create a new pull request towards the upstream master.

Thank you!