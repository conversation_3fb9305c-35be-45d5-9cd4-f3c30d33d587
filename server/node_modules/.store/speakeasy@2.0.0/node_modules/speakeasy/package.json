{"name": "speakeasy", "description": "Two-factor authentication for Node.js. One-time passcode generator (HOTP/TOTP) with support for Google Authenticator.", "version": "2.0.0", "author": {"name": "Mark <PERSON>o & Speakeasy Contributors", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://github.com/speakeasyjs/speakeasy", "bugs": "https://github.com/speakeasyjs/speakeasy/issues", "keywords": ["authentication", "google authenticator", "hmac", "hotp", "multi-factor", "one-time password", "passwords", "totp", "two factor", "two-factor", "two-factor authentication"], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/speakeasyjs/speakeasy.git"}, "main": "index.js", "engines": {"node": ">= 0.10.0"}, "dependencies": {"base32.js": "0.0.1"}, "devDependencies": {"chai": "^3.4.1", "coveralls": "^2.11.6", "istanbul": "^0.4.2", "jsdoc": "^3.3.1", "mocha": "^2.2.5", "semistandard": "^7.0.5", "snazzy": "^2.0.1"}, "scripts": {"test": "mocha", "doc": "jsdoc -c jsdoc.json && sed -i '' -e 's/․/./g' docs/speakeasy/*/*.html", "cover": "istanbul cover _mocha -- test/* -R spec", "lint": "semistandard --verbose | snazzy"}, "__npminstall_done": true, "_from": "speakeasy@2.0.0", "_resolved": "https://registry.npmmirror.com/speakeasy/-/speakeasy-2.0.0.tgz"}