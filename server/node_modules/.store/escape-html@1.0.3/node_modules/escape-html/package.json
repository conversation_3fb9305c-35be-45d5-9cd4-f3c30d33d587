{"name": "escape-html", "description": "Escape string for use in HTML", "version": "1.0.3", "license": "MIT", "keywords": ["escape", "html", "utility"], "repository": "component/escape-html", "devDependencies": {"benchmark": "1.0.0", "beautify-benchmark": "0.2.4"}, "files": ["LICENSE", "Readme.md", "index.js"], "scripts": {"bench": "node benchmark/index.js"}, "__npminstall_done": true, "_from": "escape-html@1.0.3", "_resolved": "https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz"}