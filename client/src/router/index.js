// Auth views
import Login from '../views/auth/Login.vue'
import Register from '../views/auth/Register.vue'
import ForgotPassword from '../views/auth/ForgotPassword.vue'
import ResetPassword from '../views/auth/ResetPassword.vue'
import VerifyEmail from '../views/auth/VerifyEmail.vue'

// Main views
import Dashboard from '../views/Dashboard.vue'
import Alerts from '../views/alerts/Alerts.vue'
import AlertCreate from '../views/alerts/AlertCreate.vue'
import AlertEdit from '../views/alerts/AlertEdit.vue'
import AlertView from '../views/alerts/AlertView.vue'
import Notifications from '../views/notifications/Notifications.vue'
import Profile from '../views/profile/Profile.vue'
import Settings from '../views/settings/Settings.vue'
import ApiConfigs from '../views/settings/ApiConfigs.vue'

// Layout
import AuthLayout from '../layouts/AuthLayout.vue'
import MainLayout from '../layouts/MainLayout.vue'

export const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  
  // Auth routes
  {
    path: '/auth',
    component: AuthLayout,
    meta: { guestOnly: true },
    children: [
      {
        path: 'login',
        name: 'login',
        component: Login,
        meta: { title: 'Login' }
      },
      {
        path: 'register',
        name: 'register',
        component: Register,
        meta: { title: 'Register' }
      },
      {
        path: 'forgot-password',
        name: 'forgot-password',
        component: ForgotPassword,
        meta: { title: 'Forgot Password' }
      },
      {
        path: 'reset-password',
        name: 'reset-password',
        component: ResetPassword,
        meta: { title: 'Reset Password' }
      }
    ]
  },

  // Email verification (can be accessed by authenticated users)
  {
    path: '/verify-email',
    name: 'verify-email',
    component: VerifyEmail,
    meta: { 
      title: 'Verify Email',
      requiresAuth: true
    }
  },

  // Main app routes
  {
    path: '/',
    component: MainLayout,
    meta: { 
      requiresAuth: true,
      requiresEmailVerification: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'dashboard',
        component: Dashboard,
        meta: { title: 'Dashboard' }
      },
      
      // Alert routes
      {
        path: 'alerts',
        name: 'alerts',
        component: Alerts,
        meta: { title: 'Alerts' }
      },
      {
        path: 'alerts/create',
        name: 'alert-create',
        component: AlertCreate,
        meta: { title: 'Create Alert' }
      },
      {
        path: 'alerts/:id',
        name: 'alert-view',
        component: AlertView,
        meta: { title: 'View Alert' },
        props: true
      },
      {
        path: 'alerts/:id/edit',
        name: 'alert-edit',
        component: AlertEdit,
        meta: { title: 'Edit Alert' },
        props: true
      },
      
      // Notification routes
      {
        path: 'notifications',
        name: 'notifications',
        component: Notifications,
        meta: { title: 'Notifications' }
      },
      
      // Profile routes
      {
        path: 'profile',
        name: 'profile',
        component: Profile,
        meta: { title: 'Profile' }
      },
      
      // Settings routes
      {
        path: 'settings',
        name: 'settings',
        component: Settings,
        meta: { title: 'Settings' }
      },
      {
        path: 'settings/api-configs',
        name: 'api-configs',
        component: ApiConfigs,
        meta: { title: 'API Configurations' }
      }
    ]
  },

  // 404 catch-all
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('../views/NotFound.vue'),
    meta: { title: 'Page Not Found' }
  }
]
