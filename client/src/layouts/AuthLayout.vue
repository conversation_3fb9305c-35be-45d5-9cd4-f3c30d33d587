<template>
  <div class="auth-layout">
    <div class="auth-container">
      <div class="auth-card">
        <!-- Logo and Title -->
        <div class="auth-header">
          <div class="logo">
            <n-text style="font-size: 32px;">🚨</n-text>
          </div>
          <n-h1 style="margin: 16px 0 8px 0; text-align: center;">
            SMALT Alert System
          </n-h1>
          <n-text depth="3" style="text-align: center; display: block;">
            Comprehensive alert system with AI integration
          </n-text>
        </div>

        <!-- Auth Content -->
        <div class="auth-content">
          <router-view />
        </div>

        <!-- Footer -->
        <div class="auth-footer">
          <n-text depth="3" style="font-size: 12px; text-align: center;">
            © 2024 SMALT Alert System. All rights reserved.
          </n-text>
        </div>
      </div>
    </div>

    <!-- Background Pattern -->
    <div class="auth-background">
      <div class="pattern"></div>
    </div>
  </div>
</template>

<script setup>
import { NH1, NText } from 'naive-ui'
</script>

<style scoped>
.auth-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auth-container {
  width: 100%;
  max-width: 400px;
  padding: 20px;
  position: relative;
  z-index: 1;
}

.auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 40px 32px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.auth-content {
  margin-bottom: 24px;
}

.auth-footer {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.auth-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .auth-card {
    background: rgba(31, 41, 55, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .auth-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .auth-container {
    padding: 16px;
  }
  
  .auth-card {
    padding: 32px 24px;
  }
}
</style>
