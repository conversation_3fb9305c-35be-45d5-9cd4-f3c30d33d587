<template>
  <n-layout has-sider>
    <!-- Sidebar -->
    <n-layout-sider
      bordered
      collapse-mode="width"
      :collapsed-width="64"
      :width="240"
      :collapsed="collapsed"
      show-trigger
      @collapse="collapsed = true"
      @expand="collapsed = false"
    >
      <div class="logo-container">
        <n-text v-if="!collapsed" strong style="font-size: 18px;">
          🚨 SMALT
        </n-text>
        <n-text v-else strong style="font-size: 16px;">
          🚨
        </n-text>
      </div>
      
      <n-menu
        :collapsed="collapsed"
        :collapsed-width="64"
        :collapsed-icon-size="22"
        :options="menuOptions"
        :value="activeKey"
        @update:value="handleMenuSelect"
      />
    </n-layout-sider>

    <!-- Main Content -->
    <n-layout>
      <!-- Header -->
      <n-layout-header bordered style="height: 64px; padding: 0 24px;">
        <div class="header-content">
          <div class="header-left">
            <n-breadcrumb>
              <n-breadcrumb-item
                v-for="item in breadcrumbs"
                :key="item.name"
                :clickable="!!item.to"
                @click="item.to && $router.push(item.to)"
              >
                {{ item.label }}
              </n-breadcrumb-item>
            </n-breadcrumb>
          </div>
          
          <div class="header-right">
            <!-- Notifications -->
            <n-badge :value="unreadNotifications" :max="99">
              <n-button
                quaternary
                circle
                @click="showNotifications = true"
              >
                <template #icon>
                  <n-icon><BellIcon /></n-icon>
                </template>
              </n-button>
            </n-badge>

            <!-- Theme Toggle -->
            <n-button
              quaternary
              circle
              @click="themeStore.toggleTheme()"
            >
              <template #icon>
                <n-icon><SunIcon v-if="isDark" /><MoonIcon v-else /></n-icon>
              </template>
            </n-button>

            <!-- User Menu -->
            <n-dropdown
              trigger="click"
              :options="userMenuOptions"
              @select="handleUserMenuSelect"
            >
              <n-button quaternary circle>
                <template #icon>
                  <n-avatar
                    round
                    size="small"
                    :src="userAvatar"
                    :fallback-src="defaultAvatar"
                  >
                    {{ userInitials }}
                  </n-avatar>
                </template>
              </n-button>
            </n-dropdown>
          </div>
        </div>
      </n-layout-header>

      <!-- Content -->
      <n-layout-content content-style="padding: 24px;">
        <router-view />
      </n-layout-content>
    </n-layout>

    <!-- Notifications Drawer -->
    <n-drawer
      v-model:show="showNotifications"
      :width="400"
      placement="right"
    >
      <n-drawer-content title="Notifications">
        <NotificationsList />
      </n-drawer-content>
    </n-drawer>
  </n-layout>
</template>

<script setup>
import { ref, computed, h, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  NIcon, 
  NAvatar,
  useMessage,
  useDialog,
  useLoadingBar
} from 'naive-ui'
import {
  HomeIcon,
  BellIcon,
  CogIcon,
  UserIcon,
  LogoutIcon,
  SunIcon,
  MoonIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  KeyIcon
} from '@heroicons/vue/24/outline'

import { useAuthStore } from '../stores/auth'
import { useThemeStore } from '../stores/theme'
import NotificationsList from '../components/NotificationsList.vue'

// Stores
const authStore = useAuthStore()
const themeStore = useThemeStore()
const router = useRouter()
const route = useRoute()
const message = useMessage()
const dialog = useDialog()
const loadingBar = useLoadingBar()

// State
const collapsed = ref(false)
const showNotifications = ref(false)
const unreadNotifications = ref(0)

// Computed
const isDark = computed(() => themeStore.theme === 'dark')
const activeKey = computed(() => route.name)

const userInitials = computed(() => {
  if (!authStore.user) return 'U'
  const first = authStore.user.firstName?.[0] || ''
  const last = authStore.user.lastName?.[0] || ''
  return (first + last).toUpperCase()
})

const userAvatar = computed(() => {
  // Could be user's profile picture URL
  return null
})

const defaultAvatar = computed(() => {
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(authStore.user?.firstName + ' ' + authStore.user?.lastName)}&background=2563eb&color=fff`
})

const breadcrumbs = computed(() => {
  const crumbs = []
  
  // Add breadcrumbs based on current route
  if (route.name === 'dashboard') {
    crumbs.push({ label: 'Dashboard', name: 'dashboard' })
  } else if (route.name === 'alerts') {
    crumbs.push({ label: 'Alerts', name: 'alerts' })
  } else if (route.name === 'alert-create') {
    crumbs.push({ label: 'Alerts', name: 'alerts', to: { name: 'alerts' } })
    crumbs.push({ label: 'Create Alert', name: 'alert-create' })
  } else if (route.name === 'alert-edit') {
    crumbs.push({ label: 'Alerts', name: 'alerts', to: { name: 'alerts' } })
    crumbs.push({ label: 'Edit Alert', name: 'alert-edit' })
  } else if (route.name === 'notifications') {
    crumbs.push({ label: 'Notifications', name: 'notifications' })
  } else if (route.name === 'profile') {
    crumbs.push({ label: 'Profile', name: 'profile' })
  } else if (route.name === 'settings') {
    crumbs.push({ label: 'Settings', name: 'settings' })
  }
  
  return crumbs
})

// Menu options
const menuOptions = [
  {
    label: 'Dashboard',
    key: 'dashboard',
    icon: () => h(NIcon, null, { default: () => h(HomeIcon) })
  },
  {
    label: 'Alerts',
    key: 'alerts',
    icon: () => h(NIcon, null, { default: () => h(ExclamationTriangleIcon) })
  },
  {
    label: 'Notifications',
    key: 'notifications',
    icon: () => h(NIcon, null, { default: () => h(BellIcon) })
  },
  {
    type: 'divider'
  },
  {
    label: 'Settings',
    key: 'settings-group',
    icon: () => h(NIcon, null, { default: () => h(CogIcon) }),
    children: [
      {
        label: 'Profile',
        key: 'profile',
        icon: () => h(NIcon, null, { default: () => h(UserIcon) })
      },
      {
        label: 'API Configs',
        key: 'api-configs',
        icon: () => h(NIcon, null, { default: () => h(KeyIcon) })
      },
      {
        label: 'Settings',
        key: 'settings',
        icon: () => h(NIcon, null, { default: () => h(CogIcon) })
      }
    ]
  }
]

const userMenuOptions = [
  {
    label: 'Profile',
    key: 'profile',
    icon: () => h(NIcon, null, { default: () => h(UserIcon) })
  },
  {
    label: 'Settings',
    key: 'settings',
    icon: () => h(NIcon, null, { default: () => h(CogIcon) })
  },
  {
    type: 'divider'
  },
  {
    label: 'Logout',
    key: 'logout',
    icon: () => h(NIcon, null, { default: () => h(LogoutIcon) })
  }
]

// Methods
const handleMenuSelect = (key) => {
  router.push({ name: key })
}

const handleUserMenuSelect = (key) => {
  if (key === 'logout') {
    handleLogout()
  } else {
    router.push({ name: key })
  }
}

const handleLogout = () => {
  dialog.warning({
    title: 'Confirm Logout',
    content: 'Are you sure you want to logout?',
    positiveText: 'Logout',
    negativeText: 'Cancel',
    onPositiveClick: async () => {
      loadingBar.start()
      try {
        await authStore.logout()
        message.success('Logged out successfully')
        router.push({ name: 'login' })
      } catch (error) {
        message.error('Logout failed')
      } finally {
        loadingBar.finish()
      }
    }
  })
}

// Lifecycle
onMounted(() => {
  // Load unread notifications count
  // This would typically come from a store or API call
})
</script>

<style scoped>
.logo-container {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--n-border-color);
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
