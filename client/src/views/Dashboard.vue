<template>
  <div class="dashboard">
    <!-- Welcome Section -->
    <div class="welcome-section">
      <n-h2 style="margin: 0 0 8px 0;">
        Welcome back, {{ authStore.user?.firstName }}! 👋
      </n-h2>
      <n-text depth="3">
        Here's what's happening with your alerts today.
      </n-text>
    </div>

    <!-- Stats Cards -->
    <n-grid :cols="4" :x-gap="16" :y-gap="16" responsive="screen">
      <n-grid-item :span="statsSpan">
        <n-card>
          <n-statistic label="Total Alerts" :value="stats.totalAlerts">
            <template #prefix>
              <n-icon color="#2563eb">
                <ExclamationTriangleIcon />
              </n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>

      <n-grid-item :span="statsSpan">
        <n-card>
          <n-statistic label="Active Alerts" :value="stats.activeAlerts">
            <template #prefix>
              <n-icon color="#10b981">
                <CheckCircleIcon />
              </n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>

      <n-grid-item :span="statsSpan">
        <n-card>
          <n-statistic label="Triggered Today" :value="stats.triggeredToday">
            <template #prefix>
              <n-icon color="#f59e0b">
                <BellIcon />
              </n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>

      <n-grid-item :span="statsSpan">
        <n-card>
          <n-statistic label="Notifications Sent" :value="stats.notificationsSent">
            <template #prefix>
              <n-icon color="#8b5cf6">
                <PaperAirplaneIcon />
              </n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- Main Content Grid -->
    <n-grid :cols="3" :x-gap="16" :y-gap="16" responsive="screen" style="margin-top: 24px;">
      <!-- Recent Alerts -->
      <n-grid-item :span="contentSpan">
        <n-card title="Recent Alerts" size="small">
          <template #header-extra>
            <n-button
              text
              type="primary"
              @click="$router.push({ name: 'alerts' })"
            >
              View All
            </n-button>
          </template>

          <div v-if="loading.alerts" class="loading-container">
            <n-spin size="small" />
          </div>

          <div v-else-if="recentAlerts.length === 0" class="empty-state">
            <n-empty description="No alerts yet">
              <template #extra>
                <n-button
                  type="primary"
                  @click="$router.push({ name: 'alert-create' })"
                >
                  Create Your First Alert
                </n-button>
              </template>
            </n-empty>
          </div>

          <div v-else class="alerts-list">
            <div
              v-for="alert in recentAlerts"
              :key="alert.id"
              class="alert-item"
              @click="$router.push({ name: 'alert-view', params: { id: alert.id } })"
            >
              <div class="alert-info">
                <div class="alert-name">{{ alert.name }}</div>
                <div class="alert-meta">
                  <n-tag
                    :type="getPriorityType(alert.priority)"
                    size="small"
                  >
                    {{ alert.priority }}
                  </n-tag>
                  <n-tag
                    :type="alert.isActive ? 'success' : 'default'"
                    size="small"
                  >
                    {{ alert.isActive ? 'Active' : 'Inactive' }}
                  </n-tag>
                </div>
              </div>
              <div class="alert-actions">
                <n-button
                  size="small"
                  quaternary
                  circle
                  @click.stop="toggleAlert(alert)"
                >
                  <template #icon>
                    <n-icon>
                      <PlayIcon v-if="!alert.isActive" />
                      <PauseIcon v-else />
                    </n-icon>
                  </template>
                </n-button>
              </div>
            </div>
          </div>
        </n-card>
      </n-grid-item>

      <!-- Recent Notifications -->
      <n-grid-item :span="contentSpan">
        <n-card title="Recent Notifications" size="small">
          <template #header-extra>
            <n-button
              text
              type="primary"
              @click="$router.push({ name: 'notifications' })"
            >
              View All
            </n-button>
          </template>

          <div v-if="loading.notifications" class="loading-container">
            <n-spin size="small" />
          </div>

          <div v-else-if="recentNotifications.length === 0" class="empty-state">
            <n-empty description="No notifications yet" />
          </div>

          <div v-else class="notifications-list">
            <div
              v-for="notification in recentNotifications"
              :key="notification.id"
              class="notification-item"
            >
              <div class="notification-info">
                <div class="notification-subject">{{ notification.subject }}</div>
                <div class="notification-meta">
                  <n-tag
                    :type="getNotificationStatusType(notification.status)"
                    size="small"
                  >
                    {{ notification.status }}
                  </n-tag>
                  <span class="notification-time">
                    {{ formatTime(notification.createdAt) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </n-card>
      </n-grid-item>

      <!-- Quick Actions -->
      <n-grid-item :span="contentSpan">
        <n-card title="Quick Actions" size="small">
          <div class="quick-actions">
            <n-button
              type="primary"
              block
              @click="$router.push({ name: 'alert-create' })"
            >
              <template #icon>
                <n-icon><PlusIcon /></n-icon>
              </template>
              Create New Alert
            </n-button>

            <n-button
              block
              @click="$router.push({ name: 'api-configs' })"
            >
              <template #icon>
                <n-icon><KeyIcon /></n-icon>
              </template>
              Configure APIs
            </n-button>

            <n-button
              block
              @click="$router.push({ name: 'profile' })"
            >
              <template #icon>
                <n-icon><UserIcon /></n-icon>
              </template>
              Update Profile
            </n-button>

            <n-button
              block
              @click="testNotifications"
            >
              <template #icon>
                <n-icon><BellIcon /></n-icon>
              </template>
              Test Notifications
            </n-button>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import {
  ExclamationTriangleIcon,
  CheckCircleIcon,
  BellIcon,
  PaperAirplaneIcon,
  PlayIcon,
  PauseIcon,
  PlusIcon,
  KeyIcon,
  UserIcon
} from '@heroicons/vue/24/outline'

import { useAuthStore } from '../stores/auth'
import { useAlertsStore } from '../stores/alerts'
import { alertsApi, notificationsApi } from '../services/api'
import { formatDistanceToNow } from 'date-fns'

// Stores
const authStore = useAuthStore()
const alertsStore = useAlertsStore()
const message = useMessage()

// State
const stats = ref({
  totalAlerts: 0,
  activeAlerts: 0,
  triggeredToday: 0,
  notificationsSent: 0
})

const recentAlerts = ref([])
const recentNotifications = ref([])

const loading = ref({
  alerts: false,
  notifications: false,
  stats: false
})

// Computed
const statsSpan = computed(() => {
  // Responsive grid spans
  return 'xs:4 s:2 m:1 l:1 xl:1 2xl:1'
})

const contentSpan = computed(() => {
  return 'xs:3 s:3 m:1 l:1 xl:1 2xl:1'
})

// Methods
const loadDashboardData = async () => {
  await Promise.all([
    loadStats(),
    loadRecentAlerts(),
    loadRecentNotifications()
  ])
}

const loadStats = async () => {
  loading.value.stats = true
  try {
    // This would typically be a dedicated dashboard API endpoint
    const alertsResponse = await alertsApi.getAlerts({ limit: 100 })
    const alerts = alertsResponse.data.alerts

    stats.value.totalAlerts = alerts.length
    stats.value.activeAlerts = alerts.filter(a => a.isActive).length

    // For triggered today and notifications sent, you'd need additional API calls
    // This is simplified for the example
    stats.value.triggeredToday = Math.floor(Math.random() * 10)
    stats.value.notificationsSent = Math.floor(Math.random() * 50)

  } catch (error) {
    console.error('Failed to load stats:', error)
  } finally {
    loading.value.stats = false
  }
}

const loadRecentAlerts = async () => {
  loading.value.alerts = true
  try {
    const response = await alertsApi.getAlerts({ limit: 5 })
    recentAlerts.value = response.data.alerts
  } catch (error) {
    console.error('Failed to load recent alerts:', error)
  } finally {
    loading.value.alerts = false
  }
}

const loadRecentNotifications = async () => {
  loading.value.notifications = true
  try {
    const response = await notificationsApi.getNotifications({ limit: 5 })
    recentNotifications.value = response.data.notifications
  } catch (error) {
    console.error('Failed to load recent notifications:', error)
  } finally {
    loading.value.notifications = false
  }
}

const toggleAlert = async (alert) => {
  try {
    await alertsStore.toggleAlert(alert.id)
    message.success(`Alert ${alert.isActive ? 'deactivated' : 'activated'}`)
    await loadRecentAlerts() // Refresh the list
  } catch (error) {
    message.error('Failed to toggle alert')
  }
}

const testNotifications = () => {
  message.info('Test notification sent!')
  // This would trigger a test notification
}

const getPriorityType = (priority) => {
  const types = {
    low: 'default',
    medium: 'warning',
    high: 'error',
    critical: 'error'
  }
  return types[priority] || 'default'
}

const getNotificationStatusType = (status) => {
  const types = {
    pending: 'warning',
    sent: 'success',
    failed: 'error',
    delivered: 'info'
  }
  return types[status] || 'default'
}

const formatTime = (timestamp) => {
  return formatDistanceToNow(new Date(timestamp), { addSuffix: true })
}

// Lifecycle
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 24px;
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.empty-state {
  padding: 20px;
}

.alerts-list,
.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert-item,
.notification-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid var(--n-border-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.alert-item:hover {
  background-color: var(--n-color-hover);
}

.alert-info,
.notification-info {
  flex: 1;
}

.alert-name,
.notification-subject {
  font-weight: 500;
  margin-bottom: 4px;
}

.alert-meta,
.notification-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.notification-time {
  color: var(--n-text-color-disabled);
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

@media (max-width: 768px) {
  .dashboard {
    padding: 0 8px;
  }
}
</style>
