<template>
  <div class="login-form">
    <n-h3 style="margin: 0 0 24px 0; text-align: center;">
      Sign In
    </n-h3>

    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      @submit.prevent="handleSubmit"
    >
      <n-form-item path="email" label="Email">
        <n-input
          v-model:value="formData.email"
          type="email"
          placeholder="Enter your email"
          size="large"
          :disabled="loading"
        />
      </n-form-item>

      <n-form-item path="password" label="Password">
        <n-input
          v-model:value="formData.password"
          type="password"
          placeholder="Enter your password"
          size="large"
          show-password-on="click"
          :disabled="loading"
          @keyup.enter="handleSubmit"
        />
      </n-form-item>

      <!-- MFA Token (shown when needed) -->
      <n-form-item
        v-if="showMFA"
        path="mfaToken"
        label="Authentication Code"
      >
        <n-input
          v-model:value="formData.mfaToken"
          placeholder="Enter 6-digit code"
          size="large"
          :disabled="loading"
          maxlength="6"
          @keyup.enter="handleSubmit"
        />
        <template #feedback>
          <n-text depth="3" style="font-size: 12px;">
            Enter the 6-digit code from your authenticator app
          </n-text>
        </template>
      </n-form-item>

      <n-form-item>
        <n-button
          type="primary"
          size="large"
          block
          :loading="loading"
          attr-type="submit"
        >
          {{ showMFA ? 'Verify & Sign In' : 'Sign In' }}
        </n-button>
      </n-form-item>
    </n-form>

    <!-- Additional Actions -->
    <div class="form-actions">
      <n-button
        text
        type="primary"
        @click="$router.push({ name: 'forgot-password' })"
      >
        Forgot your password?
      </n-button>
    </div>

    <n-divider style="margin: 24px 0;">
      <n-text depth="3">Don't have an account?</n-text>
    </n-divider>

    <n-button
      block
      size="large"
      @click="$router.push({ name: 'register' })"
    >
      Create Account
    </n-button>

    <!-- Error Display -->
    <n-alert
      v-if="error"
      type="error"
      style="margin-top: 16px;"
      :show-icon="false"
    >
      {{ error }}
    </n-alert>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useAuthStore } from '../../stores/auth'
import { handleApiError } from '../../services/api'

// Stores and composables
const authStore = useAuthStore()
const router = useRouter()
const route = useRoute()
const message = useMessage()

// State
const formRef = ref(null)
const loading = ref(false)
const error = ref('')
const showMFA = ref(false)

const formData = reactive({
  email: '',
  password: '',
  mfaToken: ''
})

// Validation rules
const rules = {
  email: [
    {
      required: true,
      message: 'Email is required'
    },
    {
      type: 'email',
      message: 'Please enter a valid email address'
    }
  ],
  password: [
    {
      required: true,
      message: 'Password is required'
    }
  ],
  mfaToken: [
    {
      required: showMFA,
      message: 'Authentication code is required'
    },
    {
      pattern: /^\d{6}$/,
      message: 'Authentication code must be 6 digits'
    }
  ]
}

// Methods
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
  } catch (validationError) {
    return
  }

  loading.value = true
  error.value = ''

  try {
    const credentials = {
      email: formData.email,
      password: formData.password
    }

    if (showMFA.value) {
      credentials.mfaToken = formData.mfaToken
    }

    await authStore.login(credentials)

    message.success('Login successful!')

    // Redirect to intended page or dashboard
    const redirectTo = route.query.redirect || { name: 'dashboard' }
    router.push(redirectTo)

  } catch (loginError) {
    const errorInfo = handleApiError(loginError)
    
    // Check if MFA is required
    if (loginError.response?.status === 401 && 
        loginError.response?.data?.error === 'MFA required') {
      showMFA.value = true
      error.value = 'Please enter your authentication code'
      return
    }

    error.value = errorInfo.message

    // Reset MFA if login fails
    if (showMFA.value) {
      formData.mfaToken = ''
    }

  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  formData.email = ''
  formData.password = ''
  formData.mfaToken = ''
  showMFA.value = false
  error.value = ''
}
</script>

<style scoped>
.login-form {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

/* Focus styles for better accessibility */
:deep(.n-input__input-el:focus) {
  outline: 2px solid var(--n-color-primary);
  outline-offset: 2px;
}

/* Loading state styles */
:deep(.n-button--loading) {
  pointer-events: none;
}

/* Error state styles */
:deep(.n-form-item--error .n-input) {
  border-color: var(--n-color-error);
}
</style>
