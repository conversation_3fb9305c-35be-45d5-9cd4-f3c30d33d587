import axios from 'axios'
import { useAuthStore } from '../stores/auth'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    
    if (authStore.accessToken) {
      config.headers.Authorization = `Bearer ${authStore.accessToken}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true
      
      const authStore = useAuthStore()
      
      try {
        await authStore.refreshAccessToken()
        
        // Retry original request with new token
        originalRequest.headers.Authorization = `Bearer ${authStore.accessToken}`
        return api(originalRequest)
      } catch (refreshError) {
        // Refresh failed, redirect to login
        await authStore.logout()
        window.location.href = '/auth/login'
        return Promise.reject(refreshError)
      }
    }
    
    return Promise.reject(error)
  }
)

// Auth API
export const authApi = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  logout: (data) => api.post('/auth/logout', data),
  refresh: (data) => api.post('/auth/refresh', data),
  forgotPassword: (data) => api.post('/auth/forgot-password', data),
  resetPassword: (data) => api.post('/auth/reset-password', data),
  verifyEmail: (data) => api.post('/auth/verify-email', data),
  
  // User profile
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data) => api.put('/users/profile', data),
  changePassword: (data) => api.post('/users/change-password', data),
  
  // MFA
  setupMFA: () => api.post('/users/mfa/setup'),
  verifyMFA: (data) => api.post('/users/mfa/verify', data),
  disableMFA: (data) => api.post('/users/mfa/disable', data),
  
  // Sessions
  getSessions: () => api.get('/users/sessions'),
  revokeSession: (sessionId) => api.delete(`/users/sessions/${sessionId}`)
}

// Alerts API
export const alertsApi = {
  getAlerts: (params) => api.get('/alerts', { params }),
  getAlert: (id) => api.get(`/alerts/${id}`),
  createAlert: (data) => api.post('/alerts', data),
  updateAlert: (id, data) => api.put(`/alerts/${id}`, data),
  deleteAlert: (id) => api.delete(`/alerts/${id}`)
}

// Notifications API
export const notificationsApi = {
  getNotifications: (params) => api.get('/notifications', { params }),
  getNotification: (id) => api.get(`/notifications/${id}`),
  subscribePush: (data) => api.post('/notifications/push/subscribe', data),
  unsubscribePush: (data) => api.post('/notifications/push/unsubscribe', data)
}

// Data Sources API
export const dataSourcesApi = {
  getDataSources: (params) => api.get('/data-sources', { params }),
  getDataSource: (id) => api.get(`/data-sources/${id}`),
  getCategories: () => api.get('/data-sources/categories'),
  getConditions: (type) => api.get(`/data-sources/${type}/conditions`)
}

// API Configurations API
export const apiConfigsApi = {
  getConfigs: () => api.get('/api-configs'),
  getConfig: (serviceName) => api.get(`/api-configs/${serviceName}`),
  saveConfig: (data) => api.post('/api-configs', data),
  deleteConfig: (serviceName) => api.delete(`/api-configs/${serviceName}`),
  testConfig: (serviceName) => api.post(`/api-configs/${serviceName}/test`)
}

// Error handling utility
export const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response
    
    switch (status) {
      case 400:
        return {
          message: data.message || 'Invalid request',
          details: data.details || null
        }
      case 401:
        return {
          message: 'Authentication required',
          details: null
        }
      case 403:
        return {
          message: data.message || 'Access denied',
          details: null
        }
      case 404:
        return {
          message: data.message || 'Resource not found',
          details: null
        }
      case 409:
        return {
          message: data.message || 'Conflict',
          details: null
        }
      case 422:
        return {
          message: 'Validation failed',
          details: data.details || null
        }
      case 429:
        return {
          message: 'Too many requests',
          details: null
        }
      case 500:
        return {
          message: 'Server error',
          details: null
        }
      default:
        return {
          message: data.message || 'An error occurred',
          details: null
        }
    }
  } else if (error.request) {
    // Network error
    return {
      message: 'Network error - please check your connection',
      details: null
    }
  } else {
    // Other error
    return {
      message: error.message || 'An unexpected error occurred',
      details: null
    }
  }
}

// Request helpers
export const createFormData = (data) => {
  const formData = new FormData()
  
  Object.keys(data).forEach(key => {
    const value = data[key]
    
    if (value !== null && value !== undefined) {
      if (Array.isArray(value)) {
        value.forEach(item => formData.append(`${key}[]`, item))
      } else if (typeof value === 'object' && !(value instanceof File)) {
        formData.append(key, JSON.stringify(value))
      } else {
        formData.append(key, value)
      }
    }
  })
  
  return formData
}

export const downloadFile = async (url, filename) => {
  try {
    const response = await api.get(url, {
      responseType: 'blob'
    })
    
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    window.URL.revokeObjectURL(downloadUrl)
  } catch (error) {
    throw error
  }
}

export default api
