import { createApp } from 'vue'
import { createP<PERSON> } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'

// Naive UI
import naive from 'naive-ui'

// App and styles
import App from './App.vue'
import './style.css'

// Routes
import { routes } from './router'

// Stores
import { useAuthStore } from './stores/auth'

// Create router
const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Initialize auth state if not already done
  if (!authStore.initialized) {
    await authStore.initialize()
  }

  // Check if route requires authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next({ name: 'login', query: { redirect: to.fullPath } })
    return
  }

  // Check if route requires email verification
  if (to.meta.requiresEmailVerification && !authStore.user?.isEmailVerified) {
    next({ name: 'verify-email' })
    return
  }

  // Redirect authenticated users away from auth pages
  if (to.meta.guestOnly && authStore.isAuthenticated) {
    next({ name: 'dashboard' })
    return
  }

  next()
})

// Create app
const app = createApp(App)

// Use plugins
app.use(createPinia())
app.use(router)
app.use(naive)

// Global error handler
app.config.errorHandler = (error, instance, info) => {
  console.error('Global error:', error, info)
  
  // You can send errors to a logging service here
  // For now, just log to console
}

// Mount app
app.mount('#app')
