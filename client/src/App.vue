<template>
  <n-config-provider :theme="theme" :theme-overrides="themeOverrides">
    <n-global-style />
    <n-loading-bar-provider>
      <n-dialog-provider>
        <n-notification-provider>
          <n-message-provider>
            <router-view />
          </n-message-provider>
        </n-notification-provider>
      </n-dialog-provider>
    </n-loading-bar-provider>
  </n-config-provider>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useOsTheme } from 'naive-ui'
import { useThemeStore } from './stores/theme'
import { useAuthStore } from './stores/auth'

// Stores
const themeStore = useThemeStore()
const authStore = useAuthStore()

// Theme
const osTheme = useOsTheme()

const theme = computed(() => {
  if (themeStore.theme === 'auto') {
    return osTheme.value === 'dark' ? 'dark' : null
  }
  return themeStore.theme === 'dark' ? 'dark' : null
})

const themeOverrides = {
  common: {
    primaryColor: '#2563eb',
    primaryColorHover: '#3b82f6',
    primaryColorPressed: '#1d4ed8',
    primaryColorSuppl: '#60a5fa',
    successColor: '#10b981',
    warningColor: '#f59e0b',
    errorColor: '#ef4444',
    infoColor: '#3b82f6'
  },
  Button: {
    textColor: '#ffffff'
  },
  Card: {
    borderRadius: '8px'
  }
}

// Initialize app
onMounted(async () => {
  // Initialize auth state
  await authStore.initialize()
  
  // Initialize push notifications if supported
  if ('serviceWorker' in navigator && 'PushManager' in window) {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js')
      console.log('Service Worker registered:', registration)
    } catch (error) {
      console.error('Service Worker registration failed:', error)
    }
  }
})
</script>

<style>
/* Global app styles are in style.css */
</style>
