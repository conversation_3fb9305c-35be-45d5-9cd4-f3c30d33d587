import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '../services/api'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const accessToken = ref(localStorage.getItem('accessToken'))
  const refreshToken = ref(localStorage.getItem('refreshToken'))
  const initialized = ref(false)
  const loading = ref(false)

  // Getters
  const isAuthenticated = computed(() => !!accessToken.value && !!user.value)
  const isEmailVerified = computed(() => user.value?.isEmailVerified || false)
  const hasMFA = computed(() => user.value?.mfaEnabled || false)

  // Actions
  const initialize = async () => {
    if (initialized.value) return

    if (accessToken.value) {
      try {
        await fetchProfile()
      } catch (error) {
        console.error('Failed to fetch profile during initialization:', error)
        await logout()
      }
    }

    initialized.value = true
  }

  const login = async (credentials) => {
    loading.value = true
    try {
      const response = await authApi.login(credentials)
      
      setAuthData(response.data)
      
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const register = async (userData) => {
    loading.value = true
    try {
      const response = await authApi.register(userData)
      
      setAuthData(response.data)
      
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    loading.value = true
    try {
      if (refreshToken.value) {
        await authApi.logout({ refreshToken: refreshToken.value })
      }
    } catch (error) {
      console.error('Logout API call failed:', error)
    } finally {
      clearAuthData()
      loading.value = false
    }
  }

  const refreshAccessToken = async () => {
    if (!refreshToken.value) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await authApi.refresh({ refreshToken: refreshToken.value })
      
      accessToken.value = response.data.accessToken
      localStorage.setItem('accessToken', response.data.accessToken)
      
      return response.data.accessToken
    } catch (error) {
      await logout()
      throw error
    }
  }

  const fetchProfile = async () => {
    try {
      const response = await authApi.getProfile()
      user.value = response.data
      return response.data
    } catch (error) {
      throw error
    }
  }

  const updateProfile = async (profileData) => {
    loading.value = true
    try {
      const response = await authApi.updateProfile(profileData)
      user.value = { ...user.value, ...response.data }
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const changePassword = async (passwordData) => {
    loading.value = true
    try {
      const response = await authApi.changePassword(passwordData)
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const forgotPassword = async (email) => {
    loading.value = true
    try {
      const response = await authApi.forgotPassword({ email })
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const resetPassword = async (resetData) => {
    loading.value = true
    try {
      const response = await authApi.resetPassword(resetData)
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const verifyEmail = async (token) => {
    loading.value = true
    try {
      const response = await authApi.verifyEmail({ token })
      if (user.value) {
        user.value.isEmailVerified = true
      }
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const setupMFA = async () => {
    loading.value = true
    try {
      const response = await authApi.setupMFA()
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const verifyMFA = async (token) => {
    loading.value = true
    try {
      const response = await authApi.verifyMFA({ token })
      if (user.value) {
        user.value.mfaEnabled = true
      }
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const disableMFA = async (mfaData) => {
    loading.value = true
    try {
      const response = await authApi.disableMFA(mfaData)
      if (user.value) {
        user.value.mfaEnabled = false
      }
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const getSessions = async () => {
    try {
      const response = await authApi.getSessions()
      return response.data.sessions
    } catch (error) {
      throw error
    }
  }

  const revokeSession = async (sessionId) => {
    try {
      const response = await authApi.revokeSession(sessionId)
      return response.data
    } catch (error) {
      throw error
    }
  }

  // Helper functions
  const setAuthData = (authData) => {
    user.value = authData.user
    accessToken.value = authData.accessToken
    refreshToken.value = authData.refreshToken

    localStorage.setItem('accessToken', authData.accessToken)
    localStorage.setItem('refreshToken', authData.refreshToken)
  }

  const clearAuthData = () => {
    user.value = null
    accessToken.value = null
    refreshToken.value = null

    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
  }

  return {
    // State
    user,
    accessToken,
    refreshToken,
    initialized,
    loading,

    // Getters
    isAuthenticated,
    isEmailVerified,
    hasMFA,

    // Actions
    initialize,
    login,
    register,
    logout,
    refreshAccessToken,
    fetchProfile,
    updateProfile,
    changePassword,
    forgotPassword,
    resetPassword,
    verifyEmail,
    setupMFA,
    verifyMFA,
    disableMFA,
    getSessions,
    revokeSession,

    // Helpers
    setAuthData,
    clearAuthData
  }
})
