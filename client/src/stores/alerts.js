import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { alertsApi } from '../services/api'

export const useAlertsStore = defineStore('alerts', () => {
  // State
  const alerts = ref([])
  const currentAlert = ref(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  const filters = ref({
    category: null,
    isActive: null,
    priority: null,
    search: ''
  })

  // Getters
  const activeAlerts = computed(() => 
    alerts.value.filter(alert => alert.isActive)
  )

  const alertsByPriority = computed(() => {
    const grouped = {
      critical: [],
      high: [],
      medium: [],
      low: []
    }
    
    alerts.value.forEach(alert => {
      if (grouped[alert.priority]) {
        grouped[alert.priority].push(alert)
      }
    })
    
    return grouped
  })

  const alertsCount = computed(() => ({
    total: alerts.value.length,
    active: activeAlerts.value.length,
    inactive: alerts.value.filter(alert => !alert.isActive).length
  }))

  // Actions
  const fetchAlerts = async (options = {}) => {
    loading.value = true
    try {
      const params = {
        page: options.page || pagination.value.page,
        limit: options.limit || pagination.value.limit,
        ...filters.value,
        ...options
      }

      const response = await alertsApi.getAlerts(params)
      
      alerts.value = response.data.alerts
      pagination.value = response.data.pagination

      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchAlert = async (id) => {
    loading.value = true
    try {
      const response = await alertsApi.getAlert(id)
      currentAlert.value = response.data
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const createAlert = async (alertData) => {
    loading.value = true
    try {
      const response = await alertsApi.createAlert(alertData)
      
      // Add to local state
      alerts.value.unshift(response.data)
      
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateAlert = async (id, alertData) => {
    loading.value = true
    try {
      const response = await alertsApi.updateAlert(id, alertData)
      
      // Update local state
      const index = alerts.value.findIndex(alert => alert.id === id)
      if (index !== -1) {
        alerts.value[index] = response.data
      }
      
      if (currentAlert.value?.id === id) {
        currentAlert.value = response.data
      }
      
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteAlert = async (id) => {
    loading.value = true
    try {
      await alertsApi.deleteAlert(id)
      
      // Remove from local state
      const index = alerts.value.findIndex(alert => alert.id === id)
      if (index !== -1) {
        alerts.value.splice(index, 1)
      }
      
      if (currentAlert.value?.id === id) {
        currentAlert.value = null
      }
      
      return true
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const toggleAlert = async (id) => {
    const alert = alerts.value.find(a => a.id === id)
    if (!alert) return

    return await updateAlert(id, { isActive: !alert.isActive })
  }

  const duplicateAlert = async (id) => {
    const alert = alerts.value.find(a => a.id === id)
    if (!alert) throw new Error('Alert not found')

    const duplicateData = {
      name: `${alert.name} (Copy)`,
      description: alert.description,
      categoryId: alert.categoryId,
      dataSourceId: alert.dataSourceId,
      conditions: alert.conditions,
      notificationChannels: alert.notificationChannels,
      priority: alert.priority,
      frequencyMinutes: alert.frequencyMinutes,
      maxNotificationsPerDay: alert.maxNotificationsPerDay
    }

    return await createAlert(duplicateData)
  }

  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const clearFilters = () => {
    filters.value = {
      category: null,
      isActive: null,
      priority: null,
      search: ''
    }
  }

  const setPage = (page) => {
    pagination.value.page = page
  }

  const setLimit = (limit) => {
    pagination.value.limit = limit
    pagination.value.page = 1 // Reset to first page
  }

  // Clear state
  const clearAlerts = () => {
    alerts.value = []
    currentAlert.value = null
    pagination.value = {
      page: 1,
      limit: 20,
      total: 0,
      pages: 0
    }
  }

  return {
    // State
    alerts,
    currentAlert,
    loading,
    pagination,
    filters,

    // Getters
    activeAlerts,
    alertsByPriority,
    alertsCount,

    // Actions
    fetchAlerts,
    fetchAlert,
    createAlert,
    updateAlert,
    deleteAlert,
    toggleAlert,
    duplicateAlert,
    setFilters,
    clearFilters,
    setPage,
    setLimit,
    clearAlerts
  }
})
