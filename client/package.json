{"name": "smalt-alert-client", "version": "1.0.0", "description": "Frontend for SMALT Alert System", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "naive-ui": "^2.35.0", "axios": "^1.6.2", "@vueuse/core": "^10.5.0", "chart.js": "^4.4.0", "vue-chartjs": "^5.2.0", "date-fns": "^2.30.0", "qrcode": "^1.5.3", "speakeasy": "^2.0.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "@vue/test-utils": "^2.4.2", "jsdom": "^23.0.1", "eslint": "^8.53.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "@types/qrcode": "^1.5.5"}}