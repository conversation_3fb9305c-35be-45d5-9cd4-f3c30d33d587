# SMALT Alert System - Deployment Guide

This guide covers deploying the SMALT Alert System to production environments.

## Deployment Options

### 1. Traditional VPS/Server Deployment
### 2. Docker Deployment
### 3. Cloud Platform Deployment (Vercel, Netlify, Railway)
### 4. Kubernetes Deployment

## Prerequisites

- Domain name with SSL certificate
- PostgreSQL database (NeonDB recommended)
- Email service (Resend)
- External API keys (OpenAI, OpenWeather, NewsAPI)

## Environment Configuration

### Production Environment Variables

Create a production `.env` file with secure values:

```env
# Server Configuration
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# Database (Use connection pooling in production)
DATABASE_URL=************************************/smalt_alerts?sslmode=require

# JWT Secrets (Generate new ones for production)
JWT_SECRET=your-production-jwt-secret-64-chars-minimum
JWT_REFRESH_SECRET=your-production-refresh-secret-64-chars-minimum
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# External APIs
OPENAI_API_KEY=sk-your-openai-key
OPENWEATHER_API_KEY=your-openweather-key
NEWS_API_KEY=your-newsapi-key

# Email Service
RESEND_API_KEY=re_your-resend-key
RESEND_FROM_EMAIL=<EMAIL>

# Push Notifications
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key
VAPID_SUBJECT=mailto:<EMAIL>

# Security
ENCRYPTION_KEY=your-32-character-encryption-key
MFA_ISSUER=SMALT Alert System

# CORS
CORS_ORIGIN=https://yourdomain.com

# Logging
LOG_LEVEL=info
LOG_FILE=/var/log/smalt/app.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
```

## Traditional Server Deployment

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Install nginx for reverse proxy
sudo apt install nginx

# Install certbot for SSL
sudo apt install certbot python3-certbot-nginx
```

### 2. Application Deployment

```bash
# Clone repository
git clone <your-repo-url> /var/www/smalt-alerts
cd /var/www/smalt-alerts

# Install dependencies
npm run setup

# Build frontend
npm run build

# Set up environment
cp server/.env.example server/.env
# Edit server/.env with production values

# Run database migrations
cd server && npm run db:migrate && npm run db:seed

# Set up log directory
sudo mkdir -p /var/log/smalt
sudo chown $USER:$USER /var/log/smalt
```

### 3. PM2 Configuration

Create `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [{
    name: 'smalt-alerts',
    script: 'server/src/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production'
    },
    error_file: '/var/log/smalt/error.log',
    out_file: '/var/log/smalt/out.log',
    log_file: '/var/log/smalt/combined.log',
    time: true
  }]
}
```

Start the application:

```bash
pm2 start ecosystem.config.js --env production
pm2 startup
pm2 save
```

### 4. Nginx Configuration

Create `/etc/nginx/sites-available/smalt-alerts`:

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Serve static files
    location / {
        root /var/www/smalt-alerts/client/dist;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API proxy
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Health check
    location /health {
        proxy_pass http://localhost:3000/health;
        access_log off;
    }
}
```

Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/smalt-alerts /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 5. SSL Certificate

```bash
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

## Docker Deployment

### 1. Dockerfile

Create `Dockerfile`:

```dockerfile
# Multi-stage build
FROM node:18-alpine AS builder

# Build frontend
WORKDIR /app/client
COPY client/package*.json ./
RUN npm ci --only=production
COPY client/ ./
RUN npm run build

# Build backend
WORKDIR /app/server
COPY server/package*.json ./
RUN npm ci --only=production
COPY server/ ./

# Production image
FROM node:18-alpine AS production

WORKDIR /app

# Copy built application
COPY --from=builder /app/server ./server
COPY --from=builder /app/client/dist ./client/dist

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S smalt -u 1001

# Set permissions
RUN chown -R smalt:nodejs /app
USER smalt

EXPOSE 3000

CMD ["node", "server/src/index.js"]
```

### 2. Docker Compose

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: smalt_alerts
      POSTGRES_USER: smalt
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
```

### 3. Deploy with Docker

```bash
# Build and start
docker-compose up -d

# Run migrations
docker-compose exec app npm run db:migrate

# View logs
docker-compose logs -f app
```

## Cloud Platform Deployment

### Vercel (Frontend) + Railway (Backend)

#### Frontend on Vercel

1. Connect your GitHub repository to Vercel
2. Set build command: `cd client && npm run build`
3. Set output directory: `client/dist`
4. Add environment variables in Vercel dashboard

#### Backend on Railway

1. Connect your GitHub repository to Railway
2. Set start command: `cd server && npm start`
3. Add environment variables in Railway dashboard
4. Connect to Railway PostgreSQL addon

### Render (Full-stack)

Create `render.yaml`:

```yaml
services:
  - type: web
    name: smalt-alerts
    env: node
    buildCommand: npm run setup && npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: DATABASE_URL
        fromDatabase:
          name: smalt-db
          property: connectionString

databases:
  - name: smalt-db
    databaseName: smalt_alerts
    user: smalt
```

## Monitoring and Maintenance

### 1. Health Checks

Set up monitoring for:
- Application health endpoint (`/health`)
- Database connectivity
- External API availability
- Disk space and memory usage

### 2. Logging

Configure log rotation:

```bash
# /etc/logrotate.d/smalt-alerts
/var/log/smalt/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 smalt smalt
    postrotate
        pm2 reloadLogs
    endscript
}
```

### 3. Backups

Set up automated database backups:

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump $DATABASE_URL > /backups/smalt_alerts_$DATE.sql
find /backups -name "smalt_alerts_*.sql" -mtime +7 -delete
```

### 4. Updates

Create update script:

```bash
#!/bin/bash
# update.sh
cd /var/www/smalt-alerts
git pull origin main
npm run setup
npm run build
cd server && npm run db:migrate
pm2 reload smalt-alerts
```

## Security Checklist

- [ ] Use HTTPS everywhere
- [ ] Set secure environment variables
- [ ] Configure firewall (UFW)
- [ ] Set up fail2ban
- [ ] Regular security updates
- [ ] Database connection encryption
- [ ] API rate limiting
- [ ] Input validation
- [ ] CORS configuration
- [ ] Security headers

## Performance Optimization

- [ ] Enable gzip compression
- [ ] Set up CDN for static assets
- [ ] Database query optimization
- [ ] Connection pooling
- [ ] Caching strategy
- [ ] Image optimization
- [ ] Bundle size optimization

## Troubleshooting

### Common Issues

1. **502 Bad Gateway**
   - Check if Node.js app is running
   - Verify nginx configuration
   - Check firewall settings

2. **Database Connection Issues**
   - Verify DATABASE_URL
   - Check network connectivity
   - Ensure SSL configuration

3. **High Memory Usage**
   - Monitor with `pm2 monit`
   - Check for memory leaks
   - Adjust PM2 instance count

### Useful Commands

```bash
# Check application status
pm2 status
pm2 logs smalt-alerts

# Check nginx status
sudo systemctl status nginx
sudo nginx -t

# Check database connectivity
psql $DATABASE_URL -c "SELECT 1;"

# Monitor system resources
htop
df -h
free -h
```
