# SMALT Alert System - API Documentation

## Overview

The SMALT Alert System provides a comprehensive REST API for managing alerts, notifications, and user accounts. The API is built with Express.js and uses JWT for authentication.

## Base URL

```
http://localhost:3000/api
```

## Authentication

The API uses <PERSON><PERSON><PERSON> (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Authentication Flow

1. **Register/Login** - Get access and refresh tokens
2. **Use Access Token** - Include in requests
3. **Refresh Token** - When access token expires

## Rate Limiting

- **General API**: 1000 requests per 15 minutes per user
- **Authentication**: 10 requests per 15 minutes per IP
- **Sensitive Operations**: 5 requests per 15 minutes per user

## Response Format

All API responses follow this format:

```json
{
  "data": {},
  "message": "Success message",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

Error responses:

```json
{
  "error": "Error type",
  "message": "Error description",
  "details": [],
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Endpoints

### Authentication

#### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe"
}
```

**Response:**
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "isEmailVerified": false,
    "mfaEnabled": false
  },
  "accessToken": "jwt-token",
  "refreshToken": "refresh-token",
  "expiresIn": 900
}
```

#### POST /auth/login
Authenticate user and get tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "mfaToken": "123456"
}
```

#### POST /auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "refresh-token"
}
```

#### POST /auth/logout
Logout and invalidate tokens.

**Request Body:**
```json
{
  "refreshToken": "refresh-token"
}
```

### User Management

#### GET /users/profile
Get current user profile.

**Response:**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "isEmailVerified": true,
  "mfaEnabled": false,
  "lastLogin": "2024-01-01T00:00:00.000Z",
  "createdAt": "2024-01-01T00:00:00.000Z"
}
```

#### PUT /users/profile
Update user profile.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Smith",
  "email": "<EMAIL>"
}
```

#### POST /users/change-password
Change user password.

**Request Body:**
```json
{
  "currentPassword": "OldPassword123!",
  "newPassword": "NewPassword123!"
}
```

### Multi-Factor Authentication

#### POST /users/mfa/setup
Setup MFA for user account.

**Response:**
```json
{
  "secret": "base32-secret",
  "qrCode": "data:image/png;base64,...",
  "backupCodes": ["CODE1", "CODE2", ...]
}
```

#### POST /users/mfa/verify
Verify and enable MFA.

**Request Body:**
```json
{
  "token": "123456"
}
```

#### POST /users/mfa/disable
Disable MFA for user account.

**Request Body:**
```json
{
  "password": "UserPassword123!",
  "token": "123456"
}
```

### Alerts

#### GET /alerts
Get user's alerts with pagination and filtering.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20)
- `category` (string): Filter by category
- `isActive` (boolean): Filter by active status
- `priority` (string): Filter by priority (low, medium, high, critical)

**Response:**
```json
{
  "alerts": [
    {
      "id": "uuid",
      "name": "Stock Alert",
      "description": "Monitor AAPL stock price",
      "categoryId": "uuid",
      "dataSourceId": "uuid",
      "conditions": {},
      "notificationChannels": ["email", "push"],
      "isActive": true,
      "priority": "medium",
      "frequencyMinutes": 5,
      "maxNotificationsPerDay": 10,
      "lastChecked": "2024-01-01T00:00:00.000Z",
      "lastTriggered": null,
      "triggerCount": 0,
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "pages": 1
  }
}
```

#### POST /alerts
Create a new alert.

**Request Body:**
```json
{
  "name": "Stock Alert",
  "description": "Monitor AAPL stock price",
  "categoryId": "uuid",
  "dataSourceId": "uuid",
  "conditions": {
    "symbol": "AAPL",
    "rules": [
      {
        "field": "price",
        "operator": "gt",
        "value": "150",
        "logicalOperator": "AND"
      }
    ]
  },
  "notificationChannels": ["email", "push"],
  "priority": "medium",
  "frequencyMinutes": 5,
  "maxNotificationsPerDay": 10
}
```

#### GET /alerts/:id
Get specific alert by ID.

#### PUT /alerts/:id
Update alert.

#### DELETE /alerts/:id
Delete alert.

### Notifications

#### GET /notifications
Get user's notifications with pagination and filtering.

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `type` (string): Filter by type (email, push, sms)
- `status` (string): Filter by status (pending, sent, failed, delivered)
- `alertId` (string): Filter by alert ID

**Response:**
```json
{
  "notifications": [
    {
      "id": "uuid",
      "alertId": "uuid",
      "alertName": "Stock Alert",
      "type": "email",
      "channel": "<EMAIL>",
      "subject": "Alert: Stock Alert",
      "content": "Your alert has been triggered...",
      "status": "sent",
      "sentAt": "2024-01-01T00:00:00.000Z",
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "pages": 1
  }
}
```

#### POST /notifications/push/subscribe
Subscribe to push notifications.

**Request Body:**
```json
{
  "endpoint": "https://fcm.googleapis.com/fcm/send/...",
  "keys": {
    "p256dh": "key",
    "auth": "key"
  },
  "deviceInfo": {
    "userAgent": "Mozilla/5.0...",
    "platform": "Web"
  }
}
```

### Data Sources

#### GET /data-sources
Get available data sources.

**Response:**
```json
{
  "dataSources": [
    {
      "id": "uuid",
      "name": "Yahoo Finance",
      "type": "stock",
      "apiEndpoint": "https://query1.finance.yahoo.com/v8/finance/chart/",
      "rateLimitPerHour": 2000,
      "isActive": true,
      "configuration": {},
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

#### GET /data-sources/categories
Get alert categories.

#### GET /data-sources/:type/conditions
Get sample conditions for data source type.

### API Configurations

#### GET /api-configs
Get user's API configurations.

**Response:**
```json
{
  "configs": [
    {
      "id": "uuid",
      "serviceName": "openai",
      "apiEndpoint": "https://api.openai.com/v1",
      "rateLimitPerHour": 100,
      "isActive": true,
      "hasApiKey": true,
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

#### POST /api-configs
Create or update API configuration.

**Request Body:**
```json
{
  "serviceName": "openai",
  "apiKey": "sk-...",
  "apiEndpoint": "https://api.openai.com/v1",
  "rateLimitPerHour": 100
}
```

#### DELETE /api-configs/:serviceName
Delete API configuration.

#### POST /api-configs/:serviceName/test
Test API configuration.

## Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `409` - Conflict (duplicate resource)
- `422` - Unprocessable Entity (validation failed)
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

## Webhooks

The system supports webhooks for real-time notifications:

### Webhook Events

- `alert.triggered` - When an alert is triggered
- `notification.sent` - When a notification is sent
- `notification.failed` - When a notification fails

### Webhook Payload

```json
{
  "event": "alert.triggered",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "data": {
    "alertId": "uuid",
    "userId": "uuid",
    "conditions": {},
    "triggerData": {}
  }
}
```

## SDKs and Libraries

- **JavaScript/Node.js**: Use axios or fetch
- **Python**: Use requests library
- **cURL**: Examples provided in documentation

## Interactive Documentation

Visit http://localhost:3000/api-docs for interactive Swagger documentation where you can test endpoints directly.
