# SMALT Alert System - Setup Guide

This guide will help you set up the SMALT Alert System on your local development environment or production server.

## Prerequisites

- Node.js 18+ and npm
- PostgreSQL 14+ (or NeonDB account)
- Git

## Quick Start

### 1. Clone the Repository

```bash
git clone <repository-url>
cd smalt-alert-system
```

### 2. Install Dependencies

```bash
npm run setup
```

This will install dependencies for both the server and client.

### 3. Environment Configuration

#### Server Environment

Copy the example environment file:

```bash
cp server/.env.example server/.env
```

Edit `server/.env` with your configuration:

```env
# Server Configuration
NODE_ENV=development
PORT=3000
HOST=localhost

# Database Configuration (NeonDB PostgreSQL)
DATABASE_URL=postgresql://username:<EMAIL>/smalt_alerts?sslmode=require

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# External API Keys
YAHOO_FINANCE_API_KEY=your-yahoo-finance-api-key
OPENWEATHER_API_KEY=your-openweather-api-key
NEWS_API_KEY=your-news-api-key
OPENAI_API_KEY=your-openai-api-key

# Notification Services
RESEND_API_KEY=your-resend-api-key
RESEND_FROM_EMAIL=<EMAIL>

# Web Push Notifications
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key
VAPID_SUBJECT=mailto:<EMAIL>

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-here
MFA_ISSUER=SMALT Alert System
```

#### Client Environment

Copy the example environment file:

```bash
cp client/.env.example client/.env
```

Edit `client/.env`:

```env
# API Configuration
VITE_API_BASE_URL=http://localhost:3000/api

# Web Push Notifications
VITE_VAPID_PUBLIC_KEY=your-vapid-public-key

# Application Configuration
VITE_APP_NAME=SMALT Alert System
VITE_APP_VERSION=1.0.0
```

### 4. Database Setup

Run database migrations and seed data:

```bash
cd server
npm run db:migrate
npm run db:seed
```

### 5. Generate VAPID Keys (for Push Notifications)

```bash
cd server
npx web-push generate-vapid-keys
```

Copy the generated keys to your environment files.

### 6. Start Development Servers

From the root directory:

```bash
npm run dev
```

This starts both the backend (port 3000) and frontend (port 5173) servers.

## Detailed Configuration

### Database Configuration

#### Using NeonDB (Recommended)

1. Create a NeonDB account at https://neon.tech
2. Create a new project
3. Copy the connection string to your `.env` file

#### Using Local PostgreSQL

1. Install PostgreSQL
2. Create a database: `createdb smalt_alerts`
3. Update the `DATABASE_URL` in your `.env` file

### External API Configuration

#### Yahoo Finance API

Yahoo Finance provides free access to stock data. No API key required for basic usage.

#### OpenWeatherMap API

1. Sign up at https://openweathermap.org/api
2. Get your free API key
3. Add it to your `.env` file

#### NewsAPI

1. Sign up at https://newsapi.org
2. Get your free API key (limited to 100 requests/day)
3. Add it to your `.env` file

#### OpenAI API

1. Sign up at https://platform.openai.com
2. Create an API key
3. Add it to your `.env` file
4. Note: This is a paid service

#### Resend Email Service

1. Sign up at https://resend.com
2. Create an API key
3. Verify your domain or use the sandbox
4. Add the API key to your `.env` file

### Security Configuration

#### Encryption Key

Generate a secure 32-character encryption key:

```bash
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

#### JWT Secrets

Generate secure JWT secrets:

```bash
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

## Development

### Running Tests

```bash
# Run all tests
npm test

# Run server tests only
npm run test:server

# Run client tests only
npm run test:client
```

### API Documentation

Once the server is running, visit:
- Swagger UI: http://localhost:3000/api-docs
- OpenAPI JSON: http://localhost:3000/api-docs.json

### Database Management

```bash
# Run migrations
npm run db:migrate

# Seed database
npm run db:seed

# Reset database (WARNING: Deletes all data)
npm run db:reset
```

### Logs

Server logs are written to:
- `server/logs/error.log` - Error logs
- `server/logs/combined.log` - All logs

## Production Deployment

### Environment Variables

Set `NODE_ENV=production` and update all secrets and API keys.

### Database

Ensure your production database is properly configured with SSL.

### Build Frontend

```bash
npm run build
```

### Start Production Server

```bash
npm start
```

### Process Management

Use PM2 for production process management:

```bash
npm install -g pm2
pm2 start server/src/index.js --name smalt-alerts
pm2 startup
pm2 save
```

### Reverse Proxy

Configure nginx or similar:

```nginx
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check your `DATABASE_URL`
   - Ensure the database exists
   - Verify network connectivity

2. **API Keys Not Working**
   - Verify the keys are correct
   - Check API quotas and limits
   - Ensure the services are accessible

3. **Push Notifications Not Working**
   - Verify VAPID keys are correctly configured
   - Check browser permissions
   - Ensure HTTPS in production

4. **Build Errors**
   - Clear node_modules and reinstall
   - Check Node.js version compatibility
   - Verify environment variables

### Getting Help

- Check the logs in `server/logs/`
- Review the API documentation
- Check the GitHub issues

## Next Steps

1. Configure your external API keys
2. Set up your notification preferences
3. Create your first alert
4. Test the notification system
5. Configure monitoring and backups for production
